#!/usr/bin/env python3
"""
Final System Cleanup and Optimization

Comprehensive cleanup script to remove redundant files, optimize directory structure,
and prepare the system for production deployment.
"""

import sys
import time
import argparse
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.cleanup.system_cleaner import SystemCleaner


def main():
    """Main cleanup execution"""
    parser = argparse.ArgumentParser(
        description="TJA Generator Final System Cleanup and Optimization",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python run_final_cleanup.py                    # Run full cleanup
  python run_final_cleanup.py --dry-run          # Preview cleanup actions
  python run_final_cleanup.py --report-only      # Generate report only
        """
    )
    
    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='Preview cleanup actions without actually removing files'
    )
    
    parser.add_argument(
        '--report-only',
        action='store_true',
        help='Generate cleanup report without performing cleanup'
    )
    
    parser.add_argument(
        '--output-report',
        default='cleanup_report.md',
        help='Output file for cleanup report (default: cleanup_report.md)'
    )
    
    args = parser.parse_args()
    
    print("🎯 TJA Generator - Final System Cleanup and Optimization")
    print("🧹 Removing redundant files and optimizing directory structure")
    print("📅 " + time.strftime("%Y-%m-%d %H:%M:%S"))
    print()
    
    try:
        # Initialize system cleaner
        cleaner = SystemCleaner()
        
        if args.report_only:
            print("📋 Generating cleanup report only...")
            # Run dry run to get report data
            report = cleaner.run_comprehensive_cleanup(dry_run=True)
        else:
            print(f"🚀 Running {'dry-run' if args.dry_run else 'full'} cleanup...")
            report = cleaner.run_comprehensive_cleanup(dry_run=args.dry_run)
        
        # Display results
        print("\n" + "="*60)
        print("📋 CLEANUP RESULTS")
        print("="*60)
        
        print(f"📊 Files to be removed: {len(report.redundant_files)}")
        print(f"📊 Directories to be removed: {len(report.obsolete_directories)}")
        print(f"📊 Space to be freed: {report.bytes_freed / (1024*1024):.1f} MB")
        print(f"📊 Processing time: {report.cleanup_time_seconds:.2f} seconds")
        
        if not args.dry_run and not args.report_only:
            print(f"📊 Files actually removed: {report.files_removed}")
            print(f"📊 Directories actually removed: {report.directories_removed}")
        
        print("\n📋 Optimization Actions:")
        for action in report.optimization_actions:
            print(f"   ✅ {action}")
        
        # Show sample of files to be removed
        if report.redundant_files:
            print(f"\n📋 Sample Files to Remove (showing first 10 of {len(report.redundant_files)}):")
            for file_path in report.redundant_files[:10]:
                print(f"   🗑️  {file_path}")
            
            if len(report.redundant_files) > 10:
                print(f"   ... and {len(report.redundant_files) - 10} more files")
        
        # Show directories to be removed
        if report.obsolete_directories:
            print(f"\n📋 Directories to Remove:")
            for dir_path in report.obsolete_directories:
                print(f"   🗂️  {dir_path}")
        
        # Generate detailed report
        print(f"\n📄 Generating detailed report: {args.output_report}")
        report_content = cleaner.generate_cleanup_report(report, args.output_report)
        
        print("\n" + "="*60)
        if args.dry_run:
            print("🔍 DRY RUN COMPLETED - No files were actually removed")
            print("   Run without --dry-run to perform actual cleanup")
        elif args.report_only:
            print("📋 REPORT GENERATED - No cleanup performed")
        else:
            print("✅ CLEANUP COMPLETED SUCCESSFULLY")
            print("   System optimized and ready for production deployment")
        
        print(f"📄 Detailed report saved to: {args.output_report}")
        
        return 0
        
    except Exception as e:
        print(f"\n❌ Cleanup failed: {e}")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
