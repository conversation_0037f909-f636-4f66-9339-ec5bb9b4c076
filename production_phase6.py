#!/usr/bin/env python3
"""
Production Phase 6: Complete TJA Generation System

Production-ready implementation of Phase 6 RFP specifications with:
- End-to-end inference pipeline optimized for RTX 3070
- Advanced audio preprocessing (201-dimensional features)
- Intelligent TJA post-processing with pattern coherence
- Multi-layered validation framework
- Real-time performance monitoring and benchmarking
- Production API server with comprehensive error handling
- Memory-efficient processing with resource management

Usage:
    python production_phase6.py inference audio.ogg --bpm 140 --difficulty 9
    python production_phase6.py server --port 8000
    python production_phase6.py benchmark --comprehensive
    python production_phase6.py validate --system
"""

import sys
import argparse
import logging
import time
import json
import asyncio
from pathlib import Path
from typing import Dict, Any, Optional

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Production Phase 6 imports
from src.phase6.production_inference_system import TJAInferenceSystem
from src.phase6.production_api_server import run_production_server
from src.phase6.production_benchmark import ProductionBenchmark
from src.phase6.config import create_phase6_config, validate_environment
from src.utils.resource_manager import ResourceManager, cleanup_global_resources
from src.utils.memory_monitor import MemoryMonitor


def setup_production_logging(log_level: str = "INFO", log_file: Optional[str] = None):
    """Setup production-grade logging"""
    handlers = [logging.StreamHandler(sys.stdout)]
    
    if log_file:
        handlers.append(logging.FileHandler(log_file))
    
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=handlers
    )


def validate_production_system() -> Dict[str, Any]:
    """Comprehensive production system validation"""
    print("🔍 Production System Validation")
    print("=" * 50)
    
    # Environment validation
    print("📋 Validating environment...")
    env_results = validate_environment()
    
    print(f"   Environment valid: {'✅' if env_results['valid'] else '❌'}")
    
    # Hardware validation
    print("\n🖥️  Hardware Validation:")
    
    # System memory
    import psutil
    memory = psutil.virtual_memory()
    memory_gb = memory.total / (1024**3)
    available_gb = memory.available / (1024**3)
    
    print(f"   System Memory: {memory_gb:.1f}GB total, {available_gb:.1f}GB available")
    
    if memory_gb >= 16.0:
        print("   ✅ Excellent memory capacity for production")
    elif memory_gb >= 8.0:
        print("   ✅ Good memory capacity")
    else:
        print("   ⚠️  Limited memory - may impact performance")
    
    # GPU validation
    import torch
    if torch.cuda.is_available():
        gpu_props = torch.cuda.get_device_properties(0)
        gpu_memory_gb = gpu_props.total_memory / (1024**3)
        
        print(f"   GPU: {gpu_props.name} ({gpu_memory_gb:.1f}GB)")
        
        if "RTX 3070" in gpu_props.name:
            print("   🎯 RTX 3070 detected - optimal hardware for TJA generation")
            print("   ✅ All RTX 3070 optimizations will be enabled")
        elif gpu_memory_gb >= 6.0:
            print("   ✅ Excellent GPU for TJA generation")
        elif gpu_memory_gb >= 4.0:
            print("   ✅ Good GPU for TJA generation")
        else:
            print("   ⚠️  Limited GPU memory - CPU fallback may be used")
    else:
        print("   GPU: Not available - CPU-only processing")
    
    # CPU validation
    cpu_count = psutil.cpu_count()
    print(f"   CPU Cores: {cpu_count}")
    
    if cpu_count >= 16:
        print("   ✅ Excellent CPU for concurrent processing")
    elif cpu_count >= 8:
        print("   ✅ Good CPU for processing")
    else:
        print("   ⚠️  Limited CPU cores - may impact scalability")
    
    # Storage validation
    disk = psutil.disk_usage('.')
    disk_free_gb = disk.free / (1024**3)
    print(f"   Storage: {disk_free_gb:.1f}GB free")
    
    if disk_free_gb < 5.0:
        print("   ⚠️  Low disk space - may impact temporary file processing")
    
    # Configuration validation
    print("\n⚙️  Configuration Validation:")
    try:
        config = create_phase6_config(debug_mode=False)
        print("   ✅ Configuration system working")
        print(f"   Auto-detected limits: RAM {config.resource.max_system_memory_gb:.1f}GB, "
              f"GPU {config.resource.max_gpu_memory_gb:.1f}GB")
        
        # Model validation
        model_path = Path(config.inference.model_path)
        if model_path.exists():
            print(f"   ✅ Model found: {model_path}")
        else:
            print(f"   ⚠️  Model not found: {model_path}")
            print("      Run Phase 5 training first or provide model path")
        
    except Exception as e:
        print(f"   ❌ Configuration failed: {e}")
        env_results["valid"] = False
    
    # Dependencies validation
    print("\n📦 Dependencies Validation:")
    dependencies = {
        "torch": "PyTorch for model inference",
        "numpy": "Numerical computing",
        "psutil": "System monitoring",
        "fastapi": "API server framework",
        "uvicorn": "ASGI server"
    }
    
    for dep, description in dependencies.items():
        try:
            __import__(dep)
            print(f"   ✅ {dep}: {description}")
        except ImportError:
            print(f"   ❌ {dep}: {description} - NOT AVAILABLE")
            env_results["valid"] = False
    
    # Performance targets
    print("\n🎯 Performance Targets:")
    print("   Realtime Factor: 10x (RTX 3070: 15x)")
    print("   Memory Usage: <2GB RAM, <1.5GB GPU")
    print("   Quality Score: >0.8")
    print("   API Response: <6s for 60s audio")
    
    return env_results


def run_production_inference(args) -> Dict[str, Any]:
    """Run production TJA inference"""
    print("🎵 Production TJA Inference")
    print("=" * 40)
    
    start_time = time.time()
    
    try:
        # Validate audio file
        audio_path = Path(args.audio_file)
        if not audio_path.exists():
            raise FileNotFoundError(f"Audio file not found: {audio_path}")
        
        if not audio_path.suffix.lower() in ['.ogg', '.wav', '.mp3']:
            raise ValueError("Audio file must be .ogg, .wav, or .mp3 format")
        
        file_size_mb = audio_path.stat().st_size / (1024 * 1024)
        print(f"📁 Audio file: {audio_path.name} ({file_size_mb:.1f}MB)")
        
        # Create production configuration
        config = create_phase6_config(
            experiment_name=args.experiment_name,
            debug_mode=args.debug
        )
        
        # Override model path if provided
        if args.model_path:
            config.inference.model_path = args.model_path
        
        print(f"📊 Production Configuration:")
        print(f"   Model: {config.inference.model_path}")
        print(f"   Device: {config.inference.device}")
        print(f"   Memory limits: RAM {config.resource.max_system_memory_gb:.1f}GB, "
              f"GPU {config.resource.max_gpu_memory_gb:.1f}GB")
        print(f"   Chunked inference: {config.inference.use_chunked_inference}")
        print(f"   Mixed precision: {config.inference.use_mixed_precision}")
        
        # Initialize resource management
        print("\n💾 Initializing resource management...")
        resource_manager = ResourceManager()
        
        # Check resource availability
        estimated_memory_gb = max(1.0, file_size_mb / 50.0)
        estimated_gpu_memory_gb = max(0.5, file_size_mb / 100.0)
        
        available, message = resource_manager.monitor.check_resource_availability(
            estimated_memory_gb, estimated_gpu_memory_gb
        )
        
        if not available:
            print(f"   ❌ {message}")
            return {"success": False, "error": message}
        
        print(f"   ✅ Resources available")
        
        # Initialize inference system
        print("\n🔧 Initializing production inference system...")
        
        with TJAInferenceSystem(config) as inference_system:
            # Initialize components
            inference_system.initialize_components()
            
            # Load model
            model_path = config.inference.model_path
            if Path(model_path).exists():
                inference_system.load_model(model_path)
                print("   ✅ Model loaded and optimized")
            else:
                print(f"   ⚠️  Model not found, using mock inference")
            
            # Prepare metadata
            metadata = {}
            if args.title:
                metadata["title"] = args.title
            if args.artist:
                metadata["artist"] = args.artist
            
            # Run inference
            print(f"\n🎯 Generating TJA chart...")
            print(f"   BPM: {args.bpm}")
            print(f"   Offset: {args.offset}s")
            print(f"   Difficulty: {args.difficulty}")
            print(f"   Course: {args.course_type}")
            
            result = inference_system.generate_chart(
                str(audio_path), args.bpm, args.offset, 
                args.difficulty, args.course_type, metadata
            )
            
            if result["success"]:
                # Save generated chart
                output_dir = Path(args.output_dir)
                output_dir.mkdir(parents=True, exist_ok=True)
                
                # Generate output filename
                audio_name = audio_path.stem
                output_file = output_dir / f"{audio_name}_{args.course_type}{args.difficulty}.tja"
                
                # Convert chart to TJA string and save
                tja_chart = result["tja_chart"]
                if hasattr(tja_chart, 'to_string'):
                    tja_content = tja_chart.to_string()
                else:
                    # Use postprocessor to convert
                    tja_content = inference_system.postprocessor.chart_to_tja_string(tja_chart)
                
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(tja_content)
                
                # Save detailed results
                results_file = output_dir / f"{audio_name}_results.json"
                with open(results_file, 'w') as f:
                    json.dump({
                        "performance_metrics": result["performance_metrics"],
                        "validation_results": result["validation_results"],
                        "chart_metadata": {
                            "note_count": len(tja_chart.notes) if hasattr(tja_chart, 'notes') else 0,
                            "difficulty_level": args.difficulty,
                            "course_type": args.course_type
                        }
                    }, f, indent=2, default=str)
                
                # Print results
                metrics = result["performance_metrics"]
                validation = result["validation_results"]
                
                print(f"\n✅ Inference completed successfully!")
                print(f"   Total time: {metrics['total_time']:.2f}s")
                print(f"   Realtime factor: {metrics['realtime_factor']:.1f}x")
                print(f"   Quality score: {validation['overall_score']:.3f}")
                print(f"   Chart saved: {output_file}")
                print(f"   Results saved: {results_file}")
                
                # Performance summary
                performance_summary = inference_system.get_performance_summary()
                print(f"\n📊 Performance Summary:")
                print(f"   Hardware: {performance_summary['hardware_info']['gpu_name']}")
                print(f"   Optimizations: {'✅' if performance_summary['optimization_status']['model_optimized'] else '❌'}")
                
                return result
                
            else:
                print(f"❌ Inference failed: {result['error']}")
                if "recommendations" in result:
                    print("💡 Recommendations:")
                    for rec in result["recommendations"]:
                        print(f"   • {rec}")
                
                return result
    
    except Exception as e:
        print(f"❌ Production inference failed: {e}")
        import traceback
        traceback.print_exc()
        return {"success": False, "error": str(e)}


def run_production_server(args):
    """Run production API server"""
    print("🚀 Starting Production API Server")
    print("=" * 40)
    
    print(f"📊 Server Configuration:")
    print(f"   Host: {args.host}")
    print(f"   Port: {args.port}")
    print(f"   Workers: {args.workers}")
    print(f"   Config: {args.config or 'Auto-generated'}")
    
    # Run server
    try:
        asyncio.run(run_production_server(
            host=args.host,
            port=args.port,
            config_path=args.config,
            workers=args.workers
        ))
    except KeyboardInterrupt:
        print("\n⚠️  Server stopped by user")
    except Exception as e:
        print(f"❌ Server failed: {e}")
        import traceback
        traceback.print_exc()


def run_production_benchmark(args) -> Dict[str, Any]:
    """Run production benchmark suite"""
    print("📊 Production Benchmark Suite")
    print("=" * 40)
    
    try:
        # Create configuration
        config = create_phase6_config(
            experiment_name=f"production_benchmark_{int(time.time())}",
            enable_benchmarking=True,
            debug_mode=args.debug
        )
        
        print(f"📋 Benchmark Configuration:")
        print(f"   Comprehensive: {args.comprehensive}")
        print(f"   Max test cases: {args.max_test_cases}")
        print(f"   Iterations: {args.iterations}")
        
        # Initialize systems
        print("\n🔧 Initializing systems...")
        
        with TJAInferenceSystem(config) as inference_system:
            inference_system.initialize_components()
            
            # Initialize benchmark
            benchmark = ProductionBenchmark(config.benchmark, inference_system)
            
            # Run benchmark
            print("\n🚀 Running production benchmark...")
            
            if args.comprehensive:
                results = benchmark.run_comprehensive_benchmark()
            else:
                # Run basic benchmark
                results = {
                    "categories": {
                        "speed": benchmark._run_speed_benchmark(),
                        "memory": benchmark._run_memory_benchmark()
                    }
                }
                results = benchmark._calculate_overall_results(results)
            
            # Save results
            output_dir = Path(args.output_dir)
            output_dir.mkdir(parents=True, exist_ok=True)
            
            results_file = output_dir / f"production_benchmark_{int(time.time())}.json"
            benchmark.save_benchmark_results(results, str(results_file))
            
            # Print summary
            print(f"\n📊 Benchmark Results:")
            print(f"   Overall score: {results['overall_score']:.3f}")
            print(f"   Overall passed: {'✅' if results['overall_passed'] else '❌'}")
            print(f"   Total tests: {results['total_tests']}")
            print(f"   Passed tests: {results['passed_tests']}")
            print(f"   Failed tests: {results['failed_tests']}")
            print(f"   Execution time: {results['execution_time']:.2f}s")
            
            # Hardware info
            if results.get("rtx_3070_optimized", False):
                print(f"   🎯 RTX 3070 optimizations: ENABLED")
            
            # Category breakdown
            print(f"\n📋 Category Results:")
            for category, category_results in results["categories"].items():
                score = category_results.get("average_score", 0.0)
                passed = category_results.get("passed", False)
                status = "✅" if passed else "❌"
                print(f"   {category.replace('_', ' ').title()}: {score:.3f} {status}")
            
            # Recommendations
            if "recommendations" in results:
                print(f"\n💡 Optimization Recommendations:")
                for rec in results["recommendations"][:5]:  # Show top 5
                    print(f"   • {rec}")
            
            print(f"\n📄 Results saved: {results_file}")
            
            return results
            
    except Exception as e:
        print(f"❌ Benchmark failed: {e}")
        import traceback
        traceback.print_exc()
        return {"success": False, "error": str(e)}


def main():
    """Main CLI interface for production Phase 6 system"""
    parser = argparse.ArgumentParser(
        description="Production Phase 6: Complete TJA Generation System",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # System validation
  python production_phase6.py validate --system

  # Generate TJA chart from audio
  python production_phase6.py inference audio.ogg --bpm 140 --difficulty 9

  # Start production API server
  python production_phase6.py server --port 8000 --workers 4

  # Run comprehensive benchmark
  python production_phase6.py benchmark --comprehensive
        """
    )

    # Global arguments
    parser.add_argument("--debug", action="store_true", help="Enable debug mode")
    parser.add_argument("--log-level", default="INFO", choices=["DEBUG", "INFO", "WARNING", "ERROR"])
    parser.add_argument("--log-file", help="Log file path")
    parser.add_argument("--output-dir", default="outputs/production_phase6", help="Output directory")

    # Subcommands
    subparsers = parser.add_subparsers(dest="command", help="Available commands")

    # Validate command
    validate_parser = subparsers.add_parser("validate", help="Validate production system")
    validate_parser.add_argument("--system", action="store_true", help="Full system validation")

    # Inference command
    inference_parser = subparsers.add_parser("inference", help="Run TJA inference")
    inference_parser.add_argument("audio_file", help="Path to audio file (.ogg, .wav, .mp3)")
    inference_parser.add_argument("--bpm", type=float, required=True, help="Beats per minute (60-300)")
    inference_parser.add_argument("--offset", type=float, default=0.0, help="Chart offset in seconds (-10.0 to 10.0)")
    inference_parser.add_argument("--difficulty", type=int, choices=[8, 9, 10], default=9, help="Difficulty level")
    inference_parser.add_argument("--course-type", choices=["oni", "edit"], default="oni", help="Course type")
    inference_parser.add_argument("--model-path", help="Path to trained model")
    inference_parser.add_argument("--experiment-name", default="production_inference", help="Experiment name")
    inference_parser.add_argument("--title", help="Chart title")
    inference_parser.add_argument("--artist", help="Artist name")

    # Server command
    server_parser = subparsers.add_parser("server", help="Start production API server")
    server_parser.add_argument("--host", default="0.0.0.0", help="Host address")
    server_parser.add_argument("--port", type=int, default=8000, help="Port number")
    server_parser.add_argument("--workers", type=int, default=1, help="Number of workers")
    server_parser.add_argument("--config", help="Configuration file path")

    # Benchmark command
    benchmark_parser = subparsers.add_parser("benchmark", help="Run production benchmark")
    benchmark_parser.add_argument("--comprehensive", action="store_true", help="Run comprehensive benchmark")
    benchmark_parser.add_argument("--max-test-cases", type=int, default=10, help="Maximum test cases")
    benchmark_parser.add_argument("--iterations", type=int, default=3, help="Iterations per test")

    args = parser.parse_args()

    # Setup logging
    setup_production_logging(args.log_level, args.log_file)

    # Memory monitoring
    memory_monitor = MemoryMonitor()
    memory_monitor.log_memory_status("Production Phase 6 start")

    start_time = time.time()

    try:
        if args.command == "validate":
            result = validate_production_system()
            success = result.get("valid", False)

        elif args.command == "inference":
            result = run_production_inference(args)
            success = result.get("success", False)

        elif args.command == "server":
            run_production_server(args)
            success = True  # Server runs until stopped

        elif args.command == "benchmark":
            result = run_production_benchmark(args)
            success = result.get("overall_passed", False)

        else:
            parser.print_help()
            success = False

        execution_time = time.time() - start_time

        print(f"\n⏱️  Total execution time: {execution_time:.2f} seconds")

        # Final memory status
        memory_monitor.log_memory_status("Production Phase 6 end")

        # Cleanup global resources
        print("\n🧹 Cleaning up resources...")
        cleanup_global_resources()

        if success:
            print("✅ Production Phase 6 operation completed successfully!")
            print("\n🎯 Production System Ready:")
            print("   • RTX 3070 optimized inference pipeline")
            print("   • 201-dimensional audio feature processing")
            print("   • Multi-layered validation framework")
            print("   • Real-time performance monitoring")
            print("   • Production API server capabilities")
            print("   • Comprehensive error handling and recovery")
            sys.exit(0)
        else:
            print("❌ Production Phase 6 operation failed!")
            sys.exit(1)

    except KeyboardInterrupt:
        print("\n⚠️  Operation cancelled by user")
        cleanup_global_resources()
        sys.exit(1)
    except MemoryError as e:
        print(f"\n💾 Out of memory: {e}")
        print("💡 Production recommendations:")
        print("   • Use shorter audio files (< 3 minutes)")
        print("   • Enable CPU-only inference")
        print("   • Increase system memory")
        print("   • Close other applications")
        cleanup_global_resources()
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        cleanup_global_resources()
        sys.exit(1)


if __name__ == "__main__":
    main()
