#!/usr/bin/env python3
"""
Test Trained Model

Simple test of the trained Phase 3 model
"""

import sys
import torch
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.model.tja_generator import TJAGeneratorModel
from src.model import PHASE_3_MODEL_CONFIG

def test_trained_model():
    """Test the trained model"""
    print("🎼 Testing Trained Phase 3 Model...")
    
    # Load the trained model
    model_path = Path("outputs/phase3_training/best_model.pt")
    if not model_path.exists():
        print("❌ No trained model found. Please train first.")
        return False
    
    print(f"📂 Loading model from {model_path}")
    
    try:
        # Load model
        config = PHASE_3_MODEL_CONFIG["architecture"]
        model = TJAGeneratorModel(config)
        
        # Load checkpoint
        checkpoint = torch.load(model_path, map_location='cpu')
        model.load_state_dict(checkpoint['model_state_dict'])
        
        # Move to GPU if available
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        model = model.to(device)
        model.eval()
        
        print(f"✅ Model loaded successfully on {device}")
        
        # Test inference
        batch_size = 1
        audio_len = 400
        
        # Create test input
        audio_features = torch.randn(batch_size, audio_len, 201).to(device)
        
        print(f"🎵 Testing generation with audio shape: {audio_features.shape}")
        
        # Test generation for different difficulties
        for difficulty in [0, 1, 2]:
            difficulty_name = ["Oni 8", "Oni 9", "Oni 10"][difficulty]
            print(f"\n🎯 Generating {difficulty_name} chart...")
            
            with torch.no_grad():
                try:
                    # Simple forward pass first
                    outputs = model(
                        audio_features=audio_features,
                        difficulty=torch.tensor([difficulty]).to(device),
                        return_loss=False
                    )
                    
                    print(f"    Forward pass successful: {outputs['logits'].shape}")
                    
                    # Test generation (simplified)
                    generated_sequence = torch.argmax(outputs['logits'], dim=-1)
                    
                    # Analyze generated sequence
                    sequence = generated_sequence[0].cpu().numpy()
                    non_blank_ratio = (sequence != 0).mean()
                    unique_notes = len(set(sequence))
                    
                    print(f"    Generated sequence length: {len(sequence)}")
                    print(f"    Note density: {non_blank_ratio:.2%}")
                    print(f"    Unique note types: {unique_notes}")
                    
                    # Show first 20 notes
                    preview = sequence[:20]
                    note_names = ["_", "D", "K", "D+", "K+", "R", "E", "S"]
                    preview_str = "".join(note_names[min(note, 7)] for note in preview)
                    print(f"    Preview: {preview_str}")
                    
                except Exception as e:
                    print(f"    ❌ Generation failed: {e}")
                    continue
        
        print(f"\n✅ Model testing completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Model testing failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function"""
    success = test_trained_model()
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
