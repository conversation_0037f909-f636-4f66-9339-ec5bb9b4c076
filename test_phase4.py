#!/usr/bin/env python3
"""
Phase 4 Comprehensive Test Suite

Tests for the complete TJA generation system including:
- Unit tests for individual components
- Integration tests for end-to-end workflows
- Performance tests for system optimization
- Real-world validation tests
"""

import sys
import time
import tempfile
import traceback
from pathlib import Path
from typing import List, Dict, Any, Optional
import torch
import numpy as np

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.phase4 import (
    TJAGenerationPipeline,
    QualityAssessment,
    TJAQualityMetrics,
    DeploymentManager,
    PHASE_4_CONFIG
)
from src.utils.memory_monitor import MemoryMonitor, MemoryContext


class Phase4TestSuite:
    """Comprehensive test suite for Phase 4"""
    
    def __init__(self, verbose: bool = False):
        self.verbose = verbose
        self.memory_monitor = MemoryMonitor()
        self.test_results = {
            "unit_tests": {},
            "integration_tests": {},
            "performance_tests": {},
            "total_passed": 0,
            "total_failed": 0,
            "start_time": None,
            "end_time": None
        }
        
        # Create test data directory
        self.test_data_dir = Path("data/test")
        self.test_data_dir.mkdir(parents=True, exist_ok=True)
    
    def log(self, message: str, level: str = "INFO"):
        """Log test message"""
        if self.verbose or level in ["ERROR", "FAIL"]:
            timestamp = time.strftime("%H:%M:%S")
            print(f"[{timestamp}] {level}: {message}")
    
    def run_test(self, test_name: str, test_func, *args, **kwargs) -> bool:
        """Run individual test with error handling"""
        self.log(f"Running {test_name}...")
        
        try:
            with MemoryContext(self.memory_monitor, test_name):
                result = test_func(*args, **kwargs)
                
                if result:
                    self.log(f"✅ {test_name} PASSED")
                    self.test_results["total_passed"] += 1
                    return True
                else:
                    self.log(f"❌ {test_name} FAILED", "FAIL")
                    self.test_results["total_failed"] += 1
                    return False
                    
        except Exception as e:
            self.log(f"💥 {test_name} CRASHED: {e}", "ERROR")
            if self.verbose:
                self.log(traceback.format_exc(), "ERROR")
            self.test_results["total_failed"] += 1
            return False
    
    def create_test_audio_data(self) -> str:
        """Create synthetic test audio data"""
        # Create a simple synthetic audio file for testing
        sample_rate = 44100
        duration = 10  # 10 seconds
        
        # Generate sine wave
        t = np.linspace(0, duration, int(sample_rate * duration))
        frequency = 440  # A4 note
        audio_data = np.sin(2 * np.pi * frequency * t)
        
        # Add some variation
        audio_data += 0.3 * np.sin(2 * np.pi * frequency * 2 * t)
        audio_data += 0.1 * np.random.randn(len(audio_data))
        
        # Save as numpy array (simulating audio features)
        test_file = self.test_data_dir / "test_audio_features.npy"
        np.save(test_file, audio_data)
        
        return str(test_file)
    
    def test_quality_assessment_basic(self) -> bool:
        """Test basic quality assessment functionality"""
        try:
            qa = QualityAssessment(PHASE_4_CONFIG["quality_assessment"])
            
            # Create test sequence
            test_sequence = np.array([0, 1, 0, 2, 0, 1, 2, 0, 1, 0, 2, 0] * 10)  # 120 notes
            
            # Evaluate sequence
            metrics = qa.evaluate_sequence(test_sequence, difficulty_level=9)
            
            # Validate metrics
            assert isinstance(metrics, TJAQualityMetrics)
            assert 0.0 <= metrics.overall_score <= 1.0
            assert 0.0 <= metrics.musical_accuracy <= 1.0
            assert 0.0 <= metrics.difficulty_appropriateness <= 1.0
            assert 0.0 <= metrics.pattern_coherence <= 1.0
            assert 0.0 <= metrics.rhythmic_consistency <= 1.0
            assert 0.0 <= metrics.playability_score <= 1.0
            assert 0.0 <= metrics.timing_precision <= 1.0
            
            # Check note density analysis
            assert isinstance(metrics.note_density_analysis, dict)
            assert "overall_density" in metrics.note_density_analysis
            assert "note_distribution" in metrics.note_density_analysis
            
            self.log(f"Quality metrics: overall_score={metrics.overall_score:.3f}")
            return True
            
        except Exception as e:
            self.log(f"Quality assessment test failed: {e}", "ERROR")
            return False
    
    def test_quality_assessment_edge_cases(self) -> bool:
        """Test quality assessment with edge cases"""
        try:
            qa = QualityAssessment(PHASE_4_CONFIG["quality_assessment"])
            
            # Test empty sequence
            empty_sequence = np.array([])
            if len(empty_sequence) > 0:  # Skip if empty
                metrics = qa.evaluate_sequence(empty_sequence)
                assert isinstance(metrics, TJAQualityMetrics)
            
            # Test all-blank sequence
            blank_sequence = np.zeros(100, dtype=int)
            metrics = qa.evaluate_sequence(blank_sequence)
            assert isinstance(metrics, TJAQualityMetrics)
            assert metrics.note_density_analysis["overall_density"] == 0.0
            
            # Test all-notes sequence
            notes_sequence = np.ones(100, dtype=int)
            metrics = qa.evaluate_sequence(notes_sequence)
            assert isinstance(metrics, TJAQualityMetrics)
            assert metrics.note_density_analysis["overall_density"] == 1.0
            
            # Test single note
            single_note = np.array([1])
            metrics = qa.evaluate_sequence(single_note)
            assert isinstance(metrics, TJAQualityMetrics)
            
            return True
            
        except Exception as e:
            self.log(f"Quality assessment edge cases test failed: {e}", "ERROR")
            return False
    
    def test_pipeline_initialization(self) -> bool:
        """Test pipeline initialization"""
        try:
            pipeline = TJAGenerationPipeline()
            
            # Test initialization
            success = pipeline.initialize()
            
            if not success:
                self.log("Pipeline initialization failed", "ERROR")
                return False
            
            # Check pipeline state
            assert pipeline.is_initialized
            assert pipeline.model is not None
            assert pipeline.memory_monitor is not None
            assert pipeline.quality_assessor is not None
            
            # Check pipeline status
            status = pipeline.get_pipeline_status()
            assert isinstance(status, dict)
            assert "initialized" in status
            assert status["initialized"] == True
            
            # Cleanup
            pipeline.cleanup()
            
            return True
            
        except Exception as e:
            self.log(f"Pipeline initialization test failed: {e}", "ERROR")
            return False
    
    def test_pipeline_synthetic_generation(self) -> bool:
        """Test pipeline with synthetic data"""
        try:
            pipeline = TJAGenerationPipeline()
            
            if not pipeline.initialize():
                return False
            
            # Create synthetic audio features
            batch_size = 1
            seq_len = 200
            feature_dim = 201
            
            # Create temporary feature file
            temp_features = torch.randn(seq_len, feature_dim)
            temp_file = self.test_data_dir / "synthetic_features.pt"
            torch.save({"features": temp_features}, temp_file)
            
            # Mock the audio analyzer to return our synthetic features
            original_extract = pipeline._extract_audio_features
            
            def mock_extract_features(audio_file_path):
                return temp_features
            
            pipeline._extract_audio_features = mock_extract_features
            
            # Test generation
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_audio:
                temp_audio_path = temp_audio.name
            
            try:
                result = pipeline.generate_tja(
                    audio_file_path=temp_audio_path,
                    difficulty_levels=[9],
                    metadata={"title": "Test Song", "bpm": 120}
                )
                
                # Validate result
                assert result["success"] == True
                assert "output_file_path" in result
                assert "processing_time_seconds" in result
                assert "quality_metrics" in result
                
                # Check output file exists
                output_path = Path(result["output_file_path"])
                assert output_path.exists()
                
                # Check TJA content
                with open(output_path, 'r', encoding='utf-8') as f:
                    tja_content = f.read()
                
                assert "TITLE:Test Song" in tja_content
                assert "BPM:120" in tja_content
                assert "#START" in tja_content
                assert "#END" in tja_content
                
                # Cleanup
                output_path.unlink()
                
                return True
                
            finally:
                # Restore original method
                pipeline._extract_audio_features = original_extract
                Path(temp_audio_path).unlink(missing_ok=True)
                temp_file.unlink(missing_ok=True)
                pipeline.cleanup()
            
        except Exception as e:
            self.log(f"Pipeline synthetic generation test failed: {e}", "ERROR")
            return False
    
    def test_deployment_validation(self) -> bool:
        """Test deployment validation"""
        try:
            deployment_manager = DeploymentManager()
            
            # Test system requirements validation
            system_validation = deployment_manager.validate_system_requirements()
            assert isinstance(system_validation, dict)
            assert "overall_status" in system_validation
            assert "checks" in system_validation
            
            # Test performance benchmarks
            benchmark_results = deployment_manager.run_performance_benchmark()
            assert isinstance(benchmark_results, dict)
            assert "benchmarks" in benchmark_results
            assert "overall_status" in benchmark_results
            
            # Test health status
            health_status = deployment_manager.check_health_status()
            assert isinstance(health_status, dict)
            assert "status" in health_status
            assert "checks" in health_status
            
            # Test complete deployment validation
            deployment_validation = deployment_manager.validate_deployment()
            assert isinstance(deployment_validation, dict)
            assert "overall_status" in deployment_validation
            assert "validations" in deployment_validation
            
            self.log(f"Deployment status: {deployment_validation['overall_status']}")
            
            return True
            
        except Exception as e:
            self.log(f"Deployment validation test failed: {e}", "ERROR")
            return False
    
    def test_memory_optimization(self) -> bool:
        """Test memory optimization and monitoring"""
        try:
            # Test memory monitor
            initial_stats = self.memory_monitor.get_memory_stats()
            assert isinstance(initial_stats.gpu_reserved_gb, float)
            assert isinstance(initial_stats.system_memory_percent, float)
            
            # Test memory context
            with MemoryContext(self.memory_monitor, "test_operation"):
                # Allocate some memory
                test_tensor = torch.randn(1000, 1000)
                if torch.cuda.is_available():
                    test_tensor = test_tensor.cuda()
                
                # Check memory increased
                current_stats = self.memory_monitor.get_memory_stats()
                
                # Cleanup
                del test_tensor
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
            
            # Test memory cleanup
            self.memory_monitor.cleanup_memory(aggressive=True)
            
            # Test optimal batch size calculation
            optimal_batch = self.memory_monitor.find_optimal_batch_size(
                max_sequence_length=400,
                hidden_dim=128
            )
            assert isinstance(optimal_batch, int)
            assert optimal_batch >= 1
            
            self.log(f"Optimal batch size: {optimal_batch}")
            
            return True
            
        except Exception as e:
            self.log(f"Memory optimization test failed: {e}", "ERROR")
            return False
    
    def test_configuration_validation(self) -> bool:
        """Test configuration validation"""
        try:
            # Test config structure
            config = PHASE_4_CONFIG
            
            # Check required sections
            required_sections = [
                "system", "directories", "pipeline", 
                "phase1_integration", "phase2_integration", "phase3_integration",
                "quality_assessment", "api", "cli", "logging", "monitoring"
            ]
            
            for section in required_sections:
                assert section in config, f"Missing config section: {section}"
            
            # Check directory paths
            for dir_key, dir_path in config["directories"].items():
                path = Path(dir_path)
                # Create directory if it doesn't exist (for testing)
                path.mkdir(parents=True, exist_ok=True)
                assert path.exists(), f"Directory not accessible: {dir_path}"
            
            # Check model configuration
            model_config = config["phase3_integration"]["model_config"]
            required_model_keys = [
                "audio_feature_dims", "hidden_dims", "num_attention_heads",
                "num_encoder_layers", "num_decoder_layers", "max_sequence_length"
            ]
            
            for key in required_model_keys:
                assert key in model_config, f"Missing model config key: {key}"
            
            return True
            
        except Exception as e:
            self.log(f"Configuration validation test failed: {e}", "ERROR")
            return False
    
    def run_unit_tests(self) -> bool:
        """Run all unit tests"""
        self.log("=" * 50)
        self.log("RUNNING UNIT TESTS")
        self.log("=" * 50)
        
        unit_tests = [
            ("Quality Assessment Basic", self.test_quality_assessment_basic),
            ("Quality Assessment Edge Cases", self.test_quality_assessment_edge_cases),
            ("Pipeline Initialization", self.test_pipeline_initialization),
            ("Memory Optimization", self.test_memory_optimization),
            ("Configuration Validation", self.test_configuration_validation)
        ]
        
        passed = 0
        for test_name, test_func in unit_tests:
            if self.run_test(test_name, test_func):
                passed += 1
            self.test_results["unit_tests"][test_name] = passed == len([t for t in unit_tests if t[0] == test_name])
        
        self.log(f"Unit Tests: {passed}/{len(unit_tests)} passed")
        return passed == len(unit_tests)
    
    def run_integration_tests(self) -> bool:
        """Run all integration tests"""
        self.log("=" * 50)
        self.log("RUNNING INTEGRATION TESTS")
        self.log("=" * 50)
        
        integration_tests = [
            ("Pipeline Synthetic Generation", self.test_pipeline_synthetic_generation),
            ("Deployment Validation", self.test_deployment_validation)
        ]
        
        passed = 0
        for test_name, test_func in integration_tests:
            if self.run_test(test_name, test_func):
                passed += 1
            self.test_results["integration_tests"][test_name] = passed == len([t for t in integration_tests if t[0] == test_name])
        
        self.log(f"Integration Tests: {passed}/{len(integration_tests)} passed")
        return passed == len(integration_tests)
    
    def run_performance_tests(self) -> bool:
        """Run performance tests"""
        self.log("=" * 50)
        self.log("RUNNING PERFORMANCE TESTS")
        self.log("=" * 50)
        
        try:
            # Test pipeline performance
            start_time = time.time()
            
            pipeline = TJAGenerationPipeline()
            if not pipeline.initialize():
                return False
            
            init_time = time.time() - start_time
            self.log(f"Pipeline initialization time: {init_time:.2f}s")
            
            # Test memory usage
            memory_stats = self.memory_monitor.get_memory_stats()
            self.log(f"GPU memory usage: {memory_stats.gpu_reserved_gb:.2f}GB")
            self.log(f"System memory usage: {memory_stats.system_memory_percent:.1f}%")
            
            # Performance thresholds
            performance_ok = True
            
            if init_time > 30.0:  # Should initialize within 30 seconds
                self.log("⚠️  Pipeline initialization slower than expected", "WARNING")
                performance_ok = False
            
            if memory_stats.gpu_reserved_gb > 4.0:  # Should use less than 4GB
                self.log("⚠️  GPU memory usage higher than expected", "WARNING")
                performance_ok = False
            
            if memory_stats.system_memory_percent > 80.0:  # Should use less than 80% system memory
                self.log("⚠️  System memory usage higher than expected", "WARNING")
                performance_ok = False
            
            pipeline.cleanup()
            
            self.test_results["performance_tests"]["pipeline_performance"] = performance_ok
            
            if performance_ok:
                self.log("✅ Performance tests PASSED")
            else:
                self.log("⚠️  Performance tests PASSED with warnings")
            
            return True
            
        except Exception as e:
            self.log(f"Performance tests failed: {e}", "ERROR")
            self.test_results["performance_tests"]["pipeline_performance"] = False
            return False
    
    def run_all_tests(self, test_types: List[str]) -> bool:
        """Run specified test types"""
        self.test_results["start_time"] = time.time()
        
        self.log("🧪 Phase 4 Comprehensive Test Suite")
        self.log("=" * 60)
        
        # Memory status before tests
        self.memory_monitor.log_memory_status("Before tests")
        
        all_passed = True
        
        if "unit" in test_types:
            if not self.run_unit_tests():
                all_passed = False
        
        if "integration" in test_types:
            if not self.run_integration_tests():
                all_passed = False
        
        if "performance" in test_types:
            if not self.run_performance_tests():
                all_passed = False
        
        self.test_results["end_time"] = time.time()
        
        # Final results
        self.log("=" * 60)
        self.log("FINAL TEST RESULTS")
        self.log("=" * 60)
        
        total_time = self.test_results["end_time"] - self.test_results["start_time"]
        self.log(f"Total test time: {total_time:.2f} seconds")
        self.log(f"Tests passed: {self.test_results['total_passed']}")
        self.log(f"Tests failed: {self.test_results['total_failed']}")
        
        success_rate = (self.test_results['total_passed'] / 
                       (self.test_results['total_passed'] + self.test_results['total_failed']) * 100
                       if (self.test_results['total_passed'] + self.test_results['total_failed']) > 0 else 0)
        
        self.log(f"Success rate: {success_rate:.1f}%")
        
        # Memory status after tests
        self.memory_monitor.log_memory_status("After tests")
        
        if all_passed:
            self.log("🎉 ALL TESTS PASSED!")
        else:
            self.log("❌ SOME TESTS FAILED")
        
        return all_passed


def run_phase4_tests(test_types: List[str] = None, verbose: bool = False) -> bool:
    """Run Phase 4 tests"""
    if test_types is None:
        test_types = ["unit", "integration"]
    
    test_suite = Phase4TestSuite(verbose=verbose)
    return test_suite.run_all_tests(test_types)


def main():
    """Main test runner"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Phase 4 Test Suite")
    parser.add_argument("--unit", action="store_true", help="Run unit tests")
    parser.add_argument("--integration", action="store_true", help="Run integration tests")
    parser.add_argument("--performance", action="store_true", help="Run performance tests")
    parser.add_argument("--all", action="store_true", help="Run all tests")
    parser.add_argument("-v", "--verbose", action="store_true", help="Verbose output")
    
    args = parser.parse_args()
    
    test_types = []
    if args.unit:
        test_types.append("unit")
    if args.integration:
        test_types.append("integration")
    if args.performance:
        test_types.append("performance")
    if args.all:
        test_types = ["unit", "integration", "performance"]
    
    if not test_types:
        test_types = ["unit", "integration"]  # Default
    
    success = run_phase4_tests(test_types, args.verbose)
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
