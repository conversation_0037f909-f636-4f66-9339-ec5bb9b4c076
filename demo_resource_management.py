#!/usr/bin/env python3
"""
Resource Management Demonstration

Interactive demonstration of the Phase 6 resource management system
showing real-time monitoring, dynamic allocation, and error handling.
"""

import sys
import time
import numpy as np
import torch
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.utils.resource_manager import ResourceManager, memory_efficient
from src.phase6 import create_phase6_config


def demonstrate_resource_detection():
    """Demonstrate system resource detection"""
    print("🔍 Resource Detection Demonstration")
    print("=" * 50)
    
    # Create resource manager
    resource_manager = ResourceManager()
    
    # Show detected limits
    limits = resource_manager.limits
    print(f"📊 Detected Resource Limits:")
    print(f"   System Memory: {limits.max_system_memory_gb:.1f}GB")
    print(f"   GPU Memory: {limits.max_gpu_memory_gb:.1f}GB")
    print(f"   Warning Threshold: {limits.memory_warning_threshold:.0%}")
    print(f"   Critical Threshold: {limits.memory_critical_threshold:.0%}")
    
    # Show current status
    status = resource_manager.monitor.get_current_status()
    print(f"\n💾 Current Resource Status:")
    print(f"   System Memory: {status.system_memory_used_gb:.1f}GB ({status.system_memory_percent:.1f}%)")
    print(f"   Available: {status.system_memory_available_gb:.1f}GB")
    
    if torch.cuda.is_available():
        print(f"   GPU Memory: {status.gpu_memory_used_gb:.1f}GB ({status.gpu_memory_percent:.1f}%)")
        print(f"   GPU Available: {status.gpu_memory_available_gb:.1f}GB")
    else:
        print(f"   GPU: Not available")
    
    print(f"   CPU Usage: {status.cpu_percent:.1f}%")
    
    return resource_manager


def demonstrate_dynamic_allocation(resource_manager):
    """Demonstrate dynamic resource allocation"""
    print(f"\n⚡ Dynamic Allocation Demonstration")
    print("=" * 50)
    
    # Test resource availability check
    print("🔍 Checking resource availability...")
    
    test_cases = [
        (0.5, 0.2, "Small allocation"),
        (1.0, 0.5, "Medium allocation"), 
        (2.0, 1.0, "Large allocation"),
        (100.0, 50.0, "Impossible allocation")
    ]
    
    for memory_gb, gpu_memory_gb, description in test_cases:
        available, message = resource_manager.monitor.check_resource_availability(
            memory_gb, gpu_memory_gb
        )
        
        status = "✅ Available" if available else "❌ Unavailable"
        print(f"   {description} ({memory_gb:.1f}GB RAM, {gpu_memory_gb:.1f}GB GPU): {status}")
        if not available:
            print(f"      Reason: {message}")
    
    # Test actual allocation
    print(f"\n📦 Testing resource allocation...")
    
    success, message = resource_manager.allocate_resources(
        "demo_component", memory_gb=0.5, gpu_memory_gb=0.2
    )
    
    if success:
        print(f"   ✅ Allocation successful: {message}")
        
        # Show allocated resources
        allocated = resource_manager.allocated_resources
        print(f"   📊 Currently allocated:")
        for component, allocation in allocated.items():
            print(f"      {component}: {allocation['memory_gb']:.1f}GB RAM, {allocation['gpu_memory_gb']:.1f}GB GPU")
        
        # Deallocate
        resource_manager.deallocate_resources("demo_component")
        print(f"   🗑️  Resources deallocated")
    else:
        print(f"   ❌ Allocation failed: {message}")


def demonstrate_memory_monitoring(resource_manager):
    """Demonstrate real-time memory monitoring"""
    print(f"\n📊 Memory Monitoring Demonstration")
    print("=" * 50)
    
    # Start monitoring
    print("🚀 Starting memory monitoring...")
    resource_manager.monitor.start_monitoring()
    
    # Create memory pressure
    print("💾 Creating memory pressure...")
    memory_arrays = []
    
    try:
        for i in range(3):
            # Create 100MB array
            print(f"   Creating array {i+1}/3 (100MB)...")
            array = np.random.randn(100 * 1024 * 1024 // 8)
            memory_arrays.append(array)
            
            # Show current status
            status = resource_manager.monitor.get_current_status()
            print(f"   Memory usage: {status.system_memory_used_gb:.1f}GB ({status.system_memory_percent:.1f}%)")
            
            time.sleep(1.0)
        
        # Show peak usage
        summary = resource_manager.get_resource_summary()
        print(f"\n📈 Peak memory usage: {summary['peak_usage']['peak_memory_gb']:.2f}GB")
        
        # Cleanup
        print(f"\n🧹 Cleaning up memory...")
        del memory_arrays
        resource_manager.cleanup_memory()
        
        time.sleep(2.0)  # Allow cleanup to complete
        
        # Show final status
        final_status = resource_manager.monitor.get_current_status()
        print(f"   Memory after cleanup: {final_status.system_memory_used_gb:.1f}GB ({final_status.system_memory_percent:.1f}%)")
        
    finally:
        # Stop monitoring
        resource_manager.monitor.stop_monitoring()
        print("🛑 Monitoring stopped")


@memory_efficient(memory_limit_gb=1.0)
def memory_intensive_function():
    """Example memory-intensive function with decorator"""
    print("   🔧 Running memory-intensive function...")
    
    # Create some data
    data = np.random.randn(50 * 1024 * 1024 // 8)  # 50MB
    
    # Simulate processing
    result = np.mean(data)
    
    print(f"   📊 Processed {len(data)} elements, mean: {result:.6f}")
    
    return result


def demonstrate_memory_efficient_decorator(resource_manager):
    """Demonstrate memory-efficient decorator"""
    print(f"\n🎯 Memory-Efficient Decorator Demonstration")
    print("=" * 50)
    
    print("🔍 Function with @memory_efficient decorator:")
    
    try:
        # Get status before
        before_status = resource_manager.monitor.get_current_status()
        print(f"   Memory before: {before_status.system_memory_used_gb:.1f}GB")
        
        # Run decorated function
        result = memory_intensive_function()
        
        # Get status after
        after_status = resource_manager.monitor.get_current_status()
        print(f"   Memory after: {after_status.system_memory_used_gb:.1f}GB")
        print(f"   ✅ Function completed successfully")
        
    except MemoryError as e:
        print(f"   ❌ Memory error caught by decorator: {e}")
    except Exception as e:
        print(f"   ❌ Unexpected error: {e}")


def demonstrate_configuration_adaptation():
    """Demonstrate configuration adaptation"""
    print(f"\n⚙️  Configuration Adaptation Demonstration")
    print("=" * 50)
    
    print("🔧 Creating Phase 6 configuration with resource constraints...")
    
    # Create configuration
    config = create_phase6_config(debug_mode=True)
    
    print(f"📊 Auto-detected configuration:")
    print(f"   Max System Memory: {config.resource.max_system_memory_gb:.1f}GB")
    print(f"   Max GPU Memory: {config.resource.max_gpu_memory_gb:.1f}GB")
    print(f"   Chunk Size: {config.inference.max_chunk_size}")
    print(f"   Mixed Precision: {config.inference.use_mixed_precision}")
    print(f"   CPU Fallback: {config.inference.force_cpu_fallback}")
    
    # Show preprocessing settings
    preprocessing = config.inference.preprocessing
    print(f"\n🎵 Audio Preprocessing Settings:")
    print(f"   Sample Rate: {preprocessing['sample_rate']}Hz")
    print(f"   Frame Rate: {preprocessing['frame_rate']}fps")
    
    features = preprocessing['feature_extraction']
    total_features = sum(features.values())
    print(f"   Feature Dimensions: {features['spectral_features']} + {features['rhythmic_features']} + {features['temporal_features']} = {total_features}")
    
    # Test memory-efficient config generation
    resource_manager = ResourceManager()
    
    base_config = {
        "batch_size": 8,
        "num_workers": 4,
        "pin_memory": True
    }
    
    efficient_config = resource_manager.get_memory_efficient_config(base_config)
    
    print(f"\n🔄 Configuration Adaptation:")
    print(f"   Original: {base_config}")
    print(f"   Adapted:  {efficient_config}")


def demonstrate_error_handling():
    """Demonstrate error handling and recovery"""
    print(f"\n🛡️  Error Handling Demonstration")
    print("=" * 50)
    
    resource_manager = ResourceManager()
    
    print("🧪 Testing out-of-memory detection...")
    
    # Test impossible allocation
    success, message = resource_manager.allocate_resources(
        "impossible_component", 
        memory_gb=1000.0,  # Impossible amount
        gpu_memory_gb=100.0
    )
    
    if not success:
        print(f"   ✅ Correctly detected insufficient resources: {message}")
    else:
        print(f"   ❌ Should have failed allocation")
    
    # Test resource availability with excessive requirements
    available, avail_message = resource_manager.monitor.check_resource_availability(
        required_memory_gb=500.0
    )
    
    if not available:
        print(f"   ✅ Correctly detected unavailable resources: {avail_message}")
    else:
        print(f"   ❌ Should have detected unavailable resources")
    
    print(f"\n💡 Error handling features:")
    print(f"   • Automatic resource availability checking")
    print(f"   • Graceful allocation failure handling")
    print(f"   • Memory cleanup on errors")
    print(f"   • Retry mechanisms with reduced settings")
    print(f"   • Informative error messages with recommendations")


def main():
    """Main demonstration function"""
    print("🚀 Phase 6 Resource Management Demonstration")
    print("=" * 60)
    print("This demonstration shows the comprehensive resource management")
    print("system designed for memory-constrained environments.")
    print()
    
    try:
        # Resource detection
        resource_manager = demonstrate_resource_detection()
        
        # Dynamic allocation
        demonstrate_dynamic_allocation(resource_manager)
        
        # Memory monitoring
        demonstrate_memory_monitoring(resource_manager)
        
        # Memory-efficient decorator
        demonstrate_memory_efficient_decorator(resource_manager)
        
        # Configuration adaptation
        demonstrate_configuration_adaptation()
        
        # Error handling
        demonstrate_error_handling()
        
        # Summary
        print(f"\n🎉 Resource Management Demonstration Complete!")
        print("=" * 60)
        print("✅ Key features demonstrated:")
        print("   • Automatic resource detection and limits")
        print("   • Real-time memory monitoring with alerts")
        print("   • Dynamic resource allocation and cleanup")
        print("   • Memory-efficient decorators and functions")
        print("   • Configuration adaptation based on resources")
        print("   • Comprehensive error handling and recovery")
        print()
        print("💡 The system is ready for production use with:")
        print("   • Automatic memory management")
        print("   • Graceful degradation under constraints")
        print("   • Robust error handling and recovery")
        print("   • Real-time monitoring and optimization")
        
    except Exception as e:
        print(f"\n❌ Demonstration failed: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Cleanup
        print(f"\n🧹 Performing final cleanup...")
        try:
            if 'resource_manager' in locals():
                resource_manager.cleanup_memory()
                resource_manager.monitor.stop_monitoring()
        except:
            pass
        
        print("✅ Cleanup complete")


if __name__ == "__main__":
    main()
