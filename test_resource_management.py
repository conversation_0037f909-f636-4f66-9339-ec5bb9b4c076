#!/usr/bin/env python3
"""
Resource Management Test Suite

Comprehensive testing of resource management, memory monitoring,
and dynamic allocation for Phase 6 TJA Generator.
"""

import sys
import time
import gc
import tempfile
import numpy as np
import torch
from pathlib import Path
from typing import Dict, Any

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.utils.resource_manager import ResourceManager, ResourceLimits, memory_efficient
from src.phase6 import create_phase6_config
import psutil


class ResourceManagementTester:
    """Comprehensive resource management testing"""
    
    def __init__(self):
        self.test_results = []
        self.resource_manager = None
    
    def run_all_tests(self) -> Dict[str, Any]:
        """Run all resource management tests"""
        print("🧪 Resource Management Test Suite")
        print("=" * 50)
        
        tests = [
            ("Resource Detection", self.test_resource_detection),
            ("Memory Monitoring", self.test_memory_monitoring),
            ("Dynamic Allocation", self.test_dynamic_allocation),
            ("Memory Cleanup", self.test_memory_cleanup),
            ("OOM Handling", self.test_oom_handling),
            ("Configuration Adaptation", self.test_configuration_adaptation),
            ("Memory Efficient Decorator", self.test_memory_efficient_decorator)
        ]
        
        passed_tests = 0
        total_tests = len(tests)
        
        for test_name, test_func in tests:
            print(f"\n{'='*50}")
            print(f"🔬 {test_name}")
            print("-" * 30)
            
            try:
                result = test_func()
                if result:
                    print(f"✅ {test_name}: PASSED")
                    passed_tests += 1
                else:
                    print(f"❌ {test_name}: FAILED")
                
                self.test_results.append({
                    "name": test_name,
                    "passed": result,
                    "details": getattr(result, 'details', {}) if hasattr(result, 'details') else {}
                })
                
            except Exception as e:
                print(f"❌ {test_name}: ERROR - {e}")
                self.test_results.append({
                    "name": test_name,
                    "passed": False,
                    "error": str(e)
                })
        
        # Summary
        success_rate = (passed_tests / total_tests) * 100
        
        print(f"\n{'='*50}")
        print(f"📊 Test Summary")
        print(f"{'='*50}")
        print(f"Total tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {total_tests - passed_tests}")
        print(f"Success rate: {success_rate:.1f}%")
        
        return {
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "success_rate": success_rate,
            "results": self.test_results
        }
    
    def test_resource_detection(self) -> bool:
        """Test system resource detection"""
        try:
            # Test resource limits creation
            limits = ResourceLimits()
            print(f"Default limits: RAM {limits.max_system_memory_gb:.1f}GB, GPU {limits.max_gpu_memory_gb:.1f}GB")
            
            # Test resource manager initialization
            self.resource_manager = ResourceManager()
            print(f"Resource manager initialized")
            
            # Test current status
            status = self.resource_manager.monitor.get_current_status()
            print(f"Current status: RAM {status.system_memory_used_gb:.1f}GB ({status.system_memory_percent:.1f}%)")
            
            if torch.cuda.is_available():
                print(f"GPU status: {status.gpu_memory_used_gb:.1f}GB ({status.gpu_memory_percent:.1f}%)")
            
            # Validate status
            assert status.system_memory_used_gb >= 0
            assert 0 <= status.system_memory_percent <= 100
            assert status.gpu_memory_used_gb >= 0
            
            return True
            
        except Exception as e:
            print(f"Resource detection failed: {e}")
            return False
    
    def test_memory_monitoring(self) -> bool:
        """Test memory monitoring functionality"""
        try:
            if not self.resource_manager:
                self.resource_manager = ResourceManager()
            
            # Test monitoring start/stop
            print("Starting monitoring...")
            self.resource_manager.monitor.start_monitoring()
            
            # Wait for some monitoring data
            time.sleep(2.0)
            
            # Check if monitoring is active
            assert self.resource_manager.monitor.monitoring_active
            print("Monitoring active: ✅")
            
            # Check resource history
            history_length = len(self.resource_manager.monitor.resource_history)
            print(f"Resource history entries: {history_length}")
            assert history_length > 0
            
            # Test resource summary
            summary = self.resource_manager.get_resource_summary()
            print(f"Peak memory usage: {summary['peak_usage']['peak_memory_gb']:.2f}GB")
            
            # Stop monitoring
            self.resource_manager.monitor.stop_monitoring()
            print("Monitoring stopped: ✅")
            
            return True
            
        except Exception as e:
            print(f"Memory monitoring test failed: {e}")
            return False
    
    def test_dynamic_allocation(self) -> bool:
        """Test dynamic resource allocation"""
        try:
            if not self.resource_manager:
                self.resource_manager = ResourceManager()
            
            # Test resource allocation
            print("Testing resource allocation...")
            
            success, message = self.resource_manager.allocate_resources(
                "test_component", memory_gb=0.5, gpu_memory_gb=0.2
            )
            
            print(f"Allocation result: {success} - {message}")
            assert success, f"Allocation failed: {message}"
            
            # Test resource availability check
            available, avail_message = self.resource_manager.monitor.check_resource_availability(
                required_memory_gb=0.1, required_gpu_memory_gb=0.1
            )
            
            print(f"Availability check: {available} - {avail_message}")
            
            # Test deallocation
            self.resource_manager.deallocate_resources("test_component")
            print("Resource deallocation: ✅")
            
            return True
            
        except Exception as e:
            print(f"Dynamic allocation test failed: {e}")
            return False
    
    def test_memory_cleanup(self) -> bool:
        """Test memory cleanup functionality"""
        try:
            if not self.resource_manager:
                self.resource_manager = ResourceManager()
            
            # Get initial memory usage
            initial_status = self.resource_manager.monitor.get_current_status()
            initial_memory = initial_status.system_memory_used_gb
            
            print(f"Initial memory: {initial_memory:.2f}GB")
            
            # Create some memory pressure
            print("Creating memory pressure...")
            large_arrays = []
            for i in range(5):
                # Create 50MB arrays
                array = np.random.randn(50 * 1024 * 1024 // 8)  # 50MB
                large_arrays.append(array)
            
            # Check memory increase
            pressure_status = self.resource_manager.monitor.get_current_status()
            pressure_memory = pressure_status.system_memory_used_gb
            print(f"Memory under pressure: {pressure_memory:.2f}GB")
            
            # Cleanup
            print("Performing cleanup...")
            del large_arrays
            self.resource_manager.cleanup_memory()
            
            # Check memory after cleanup
            time.sleep(1.0)  # Allow time for cleanup
            cleanup_status = self.resource_manager.monitor.get_current_status()
            cleanup_memory = cleanup_status.system_memory_used_gb
            
            print(f"Memory after cleanup: {cleanup_memory:.2f}GB")
            
            # Verify cleanup was effective
            memory_freed = pressure_memory - cleanup_memory
            print(f"Memory freed: {memory_freed:.2f}GB")
            
            return memory_freed > 0.1  # At least 100MB freed
            
        except Exception as e:
            print(f"Memory cleanup test failed: {e}")
            return False
    
    def test_oom_handling(self) -> bool:
        """Test out-of-memory handling"""
        try:
            if not self.resource_manager:
                self.resource_manager = ResourceManager()
            
            print("Testing OOM detection...")
            
            # Test resource availability with excessive requirements
            available, message = self.resource_manager.monitor.check_resource_availability(
                required_memory_gb=1000.0  # Impossible requirement
            )
            
            print(f"Excessive requirement check: {available} - {message}")
            assert not available, "Should detect insufficient resources"
            
            # Test allocation failure
            success, alloc_message = self.resource_manager.allocate_resources(
                "oom_test", memory_gb=1000.0
            )
            
            print(f"Excessive allocation: {success} - {alloc_message}")
            assert not success, "Should fail to allocate excessive resources"
            
            return True
            
        except Exception as e:
            print(f"OOM handling test failed: {e}")
            return False
    
    def test_configuration_adaptation(self) -> bool:
        """Test configuration adaptation based on resources"""
        try:
            if not self.resource_manager:
                self.resource_manager = ResourceManager()
            
            print("Testing configuration adaptation...")
            
            # Test Phase 6 config creation with resource constraints
            config = create_phase6_config(debug_mode=True)
            
            print(f"Auto-detected limits: RAM {config.resource.max_system_memory_gb:.1f}GB, GPU {config.resource.max_gpu_memory_gb:.1f}GB")
            
            # Verify reasonable limits
            assert config.resource.max_system_memory_gb > 0
            assert config.resource.max_gpu_memory_gb >= 0
            
            # Test memory-efficient configuration
            base_config = {"batch_size": 8, "num_workers": 4}
            efficient_config = self.resource_manager.get_memory_efficient_config(base_config)
            
            print(f"Original config: {base_config}")
            print(f"Efficient config: {efficient_config}")
            
            # Should reduce resource usage
            assert efficient_config["batch_size"] <= base_config["batch_size"]
            
            return True
            
        except Exception as e:
            print(f"Configuration adaptation test failed: {e}")
            return False
    
    @memory_efficient(memory_limit_gb=0.5)
    def _test_memory_function(self):
        """Test function with memory efficient decorator"""
        # Create some memory usage
        data = np.random.randn(10 * 1024 * 1024 // 8)  # 10MB
        return len(data)
    
    def test_memory_efficient_decorator(self) -> bool:
        """Test memory efficient decorator"""
        try:
            print("Testing memory efficient decorator...")
            
            # Test decorator functionality
            result = self._test_memory_function()
            print(f"Decorator test result: {result}")
            
            assert result > 0, "Function should return valid result"
            
            return True
            
        except Exception as e:
            print(f"Memory efficient decorator test failed: {e}")
            return False
    
    def cleanup(self):
        """Cleanup test resources"""
        if self.resource_manager:
            self.resource_manager.cleanup_memory()
            self.resource_manager.monitor.stop_monitoring()


def run_stress_test(duration_seconds: int = 30):
    """Run stress test to validate resource management under load"""
    print(f"\n🔥 Stress Test ({duration_seconds}s)")
    print("=" * 40)
    
    resource_manager = ResourceManager()
    
    try:
        start_time = time.time()
        allocations = []
        
        while time.time() - start_time < duration_seconds:
            try:
                # Allocate memory
                size_mb = np.random.randint(10, 100)  # 10-100MB
                data = np.random.randn(size_mb * 1024 * 1024 // 8)
                allocations.append(data)
                
                # Occasionally cleanup
                if len(allocations) > 10:
                    # Remove some allocations
                    for _ in range(5):
                        if allocations:
                            allocations.pop(0)
                    
                    resource_manager.cleanup_memory()
                
                # Check status
                status = resource_manager.monitor.get_current_status()
                print(f"Memory: {status.system_memory_used_gb:.1f}GB ({status.system_memory_percent:.1f}%)", end="\r")
                
                time.sleep(0.1)
                
            except MemoryError:
                print(f"\nMemory limit reached - cleaning up...")
                allocations.clear()
                resource_manager.cleanup_memory()
                time.sleep(1.0)
        
        print(f"\n✅ Stress test completed successfully")
        return True
        
    except Exception as e:
        print(f"\n❌ Stress test failed: {e}")
        return False
    finally:
        # Cleanup
        allocations.clear()
        resource_manager.cleanup_memory()


def main():
    """Main test function"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Resource Management Test Suite")
    parser.add_argument("--stress", action="store_true", help="Run stress test")
    parser.add_argument("--duration", type=int, default=30, help="Stress test duration (seconds)")
    
    args = parser.parse_args()
    
    tester = ResourceManagementTester()
    
    try:
        if args.stress:
            run_stress_test(args.duration)
        else:
            results = tester.run_all_tests()
            
            # Save results
            import json
            results_file = Path("resource_test_results.json")
            with open(results_file, 'w') as f:
                json.dump(results, f, indent=2, default=str)
            
            print(f"\n📄 Results saved to: {results_file}")
            
            # Exit with appropriate code
            if results["success_rate"] >= 80.0:
                print(f"🎉 Resource management tests passed!")
                sys.exit(0)
            else:
                print(f"⚠️  Some resource management tests failed")
                sys.exit(1)
    
    finally:
        tester.cleanup()


if __name__ == "__main__":
    main()
