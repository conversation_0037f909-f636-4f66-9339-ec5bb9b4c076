#!/usr/bin/env python3
"""
TJA Generator - Phase 2: Audio Feature Extraction and Temporal Alignment
Main entry point for Phase 2 processing pipeline

Hardware-optimized for RTX 3070 system with 32GB RAM.
Extracts [T, 201] feature tensors from 2,800 audio files with precise temporal alignment.
"""

import sys
import time
import argparse
import logging
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.pipeline.audio_analyzer import TjaAudioFeatureAnalyzer
from src.utils.hardware_monitor import get_system_info, setup_hardware_optimized_processing
from src.paths.path_manager import PathManager, PathType


def setup_logging(log_level: str = "INFO") -> logging.Logger:
    """Setup logging configuration"""
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    # Configure root logger
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_dir / "phase2_main.log"),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    return logging.getLogger(__name__)


def validate_phase1_outputs():
    """Validate Phase 1 outputs are available"""
    print("=" * 60)
    print("PHASE 2: PHASE 1 OUTPUT VALIDATION")
    print("=" * 60)
    
    # Check for Phase 1 catalog using standardized paths
    path_manager = PathManager()
    catalog_path = path_manager.get_standardized_path(PathType.DATA_PROCESSED, "catalog.json")
    if not catalog_path.exists():
        print(f"❌ Phase 1 catalog not found: {catalog_path}")
        return False
    
    print(f"✅ Phase 1 catalog found: {catalog_path}")
    
    # Check catalog content
    try:
        import json
        with open(catalog_path, 'r', encoding='utf-8') as f:
            catalog = json.load(f)
        
        songs = catalog.get("songs", [])
        phase2_ready = sum(1 for song in songs if song.get("phase_2_ready", False))
        
        print(f"📊 Total songs in catalog: {len(songs)}")
        print(f"🎯 Phase 2 ready songs: {phase2_ready}")
        print(f"📈 Phase 1 success rate: {catalog.get('processing_statistics', {}).get('success_rate', 0):.1%}")
        
        if phase2_ready == 0:
            print("❌ No songs ready for Phase 2 processing")
            return False
        
        print("✅ Phase 1 outputs validated successfully")
        return True
        
    except Exception as e:
        print(f"❌ Error validating Phase 1 catalog: {e}")
        return False


def validate_hardware_environment():
    """Validate hardware environment for Phase 2"""
    print("\nHARDWARE ENVIRONMENT VALIDATION")
    print("=" * 60)
    
    # Get system information
    system_info = get_system_info()
    
    print("Hardware Configuration:")
    print(f"  CPU Cores: {system_info['cpu']['physical_cores']} physical, {system_info['cpu']['logical_cores']} logical")
    print(f"  Memory: {system_info['memory']['total_gb']:.1f}GB total, {system_info['memory']['available_gb']:.1f}GB available")
    print(f"  GPU: {'Available' if system_info['gpu']['cuda_available'] else 'Not Available'}")
    
    if system_info['gpu']['cuda_available']:
        print(f"    Name: {system_info['gpu']['name']}")
        print(f"    Memory: {system_info['gpu']['memory_total_gb']:.1f}GB")
    
    # Validate requirements for Phase 2
    warnings = []
    
    if system_info['cpu']['logical_cores'] < 8:
        warnings.append(f"Recommended 8+ logical cores for Phase 2, found {system_info['cpu']['logical_cores']}")
    
    if system_info['memory']['total_gb'] < 16:
        warnings.append(f"Recommended 16GB+ RAM for Phase 2, found {system_info['memory']['total_gb']:.1f}GB")
    
    if not system_info['gpu']['cuda_available']:
        warnings.append("CUDA not available - GPU acceleration disabled (Phase 2 will be slower)")
    elif "RTX 3070" not in system_info['gpu']['name']:
        warnings.append(f"Expected RTX 3070, found: {system_info['gpu']['name']}")
    
    if warnings:
        print("\nWarnings:")
        for warning in warnings:
            print(f"  ⚠️  {warning}")
    else:
        print("\n✅ Hardware configuration optimal for Phase 2")
    
    print("=" * 60)
    return len(warnings) == 0


def run_phase2_processing(catalog_path: str, test_mode: bool = False, test_count: int = 50):
    """Run the main Phase 2 processing pipeline"""
    print("PHASE 2: AUDIO FEATURE EXTRACTION AND TEMPORAL ALIGNMENT")
    print("=" * 60)
    
    start_time = time.time()
    
    # Initialize TJA audio feature analyzer with hardware optimization
    config = setup_hardware_optimized_processing()
    analyzer = TjaAudioFeatureAnalyzer(config)

    print(f"🚀 Initialized TjaAudioFeatureAnalyzer with hardware optimization")
    print(f"💾 Batch size: {config['batch_size']} songs")
    print(f"🔧 Workers: {config['parallel_workers']}")
    print(f"🎯 Target: [T, 201] feature tensors")
    
    if test_mode:
        print(f"🧪 Test mode: Processing {test_count} songs only")
    
    # Load Phase 1 catalog
    print(f"\n📂 Loading Phase 1 catalog from {catalog_path}...")
    catalog = analyzer.load_phase1_catalog(catalog_path)
    
    if catalog["eligible_songs"] == 0:
        print("❌ No songs eligible for Phase 2 processing")
        return False
    
    print(f"📊 Loaded catalog: {catalog['eligible_songs']} eligible songs")
    
    # Process audio features
    print(f"\n🎵 Starting audio feature extraction...")
    results = analyzer.process_audio_features(catalog, test_mode=test_mode, test_count=test_count)
    
    # Display results
    processing_time = time.time() - start_time
    stats = results["statistics"]
    
    print("\n" + "=" * 60)
    print("PHASE 2 PROCESSING RESULTS")
    print("=" * 60)
    print(f"📈 Total songs processed: {stats['processed_songs']}")
    print(f"✅ Successful extractions: {stats['successful_extractions']} ({stats['feature_extraction_success_rate']:.1%})")
    print(f"❌ Failed extractions: {stats['failed_extractions']}")
    print(f"⏱️  Total processing time: {processing_time:.1f}s")
    print(f"🚄 Average processing time: {stats['average_processing_time']:.2f}s per song")
    print(f"🎯 Temporal alignment accuracy: {stats.get('temporal_alignment_accuracy', 0):.1%}")
    
    # Hardware performance
    hardware_perf = results["hardware_performance"]
    print(f"\n🖥️  Hardware Performance:")
    print(f"  Processing speed: {hardware_perf.get('files_per_second', 0):.1f} songs/second")
    print(f"  Total processed: {hardware_perf.get('total_processed', 0)} items")
    
    # Feature extraction quality
    batch_stats = results["batch_processor_stats"]
    print(f"\n🎵 Feature Extraction Quality:")
    print(f"  Success rate: {batch_stats.get('success_rate', 0):.1%}")
    print(f"  Songs per minute: {batch_stats.get('songs_per_minute', 0):.1f}")
    
    # Generate Phase 3 catalog
    print(f"\n📋 Generating Phase 3 input catalog...")
    phase3_catalog = analyzer.generate_phase3_catalog(results)
    
    phase3_ready = sum(1 for song in phase3_catalog["songs"] if song["phase3_ready"])
    print(f"🎯 Phase 3 ready songs: {phase3_ready}/{len(phase3_catalog['songs'])}")
    
    print("=" * 60)
    print("✅ Phase 2 processing complete!")
    print(f"📄 Results saved to: data/processed/audio_features/")
    print(f"📊 Feature tensors: data/processed/audio_features/feature_tensors/")
    print(f"📋 Phase 3 catalog: data/processed/audio_features/phase3_input_catalog.json")
    
    return True


def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(
        description="TJA Generator Phase 2: Audio Feature Extraction and Temporal Alignment",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main_phase2.py                          # Process all Phase 1 songs
  python main_phase2.py --test                   # Test mode (50 songs)
  python main_phase2.py --test --count 100       # Test mode (100 songs)
  python main_phase2.py --catalog custom.json    # Custom Phase 1 catalog
  python main_phase2.py --validate-only          # Validation only
        """
    )
    
    parser.add_argument(
        '--catalog',
        default=None,
        help='Path to Phase 1 catalog (default: standardized data/processed/catalog.json)'
    )
    
    parser.add_argument(
        '--test', 
        action='store_true',
        help='Run in test mode (process subset of songs)'
    )
    
    parser.add_argument(
        '--count', 
        type=int, 
        default=50,
        help='Number of songs to process in test mode (default: 50)'
    )
    
    parser.add_argument(
        '--validate-only', 
        action='store_true',
        help='Only validate Phase 1 outputs and hardware, do not process'
    )
    
    parser.add_argument(
        '--log-level', 
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        default='INFO',
        help='Logging level (default: INFO)'
    )
    
    args = parser.parse_args()
    
    # Setup logging
    logger = setup_logging(args.log_level)
    
    try:
        print("🎯 TJA Generator - Phase 2: Audio Feature Extraction and Temporal Alignment")
        print("🔧 Hardware-optimized for RTX 3070 with 32GB RAM")
        print("📅 " + time.strftime("%Y-%m-%d %H:%M:%S"))
        print()
        
        # Validate Phase 1 outputs
        phase1_ok = validate_phase1_outputs()
        if not phase1_ok:
            print("❌ Phase 1 validation failed. Please run Phase 1 first.")
            return 1
        
        # Validate hardware environment
        hardware_ok = validate_hardware_environment()
        if not hardware_ok:
            print("⚠️  Hardware validation warnings detected")
            print("   Processing will continue but performance may be suboptimal")
            print()
        
        if args.validate_only:
            print("✅ Validation complete. Exiting (--validate-only specified).")
            return 0
        
        # Run processing
        success = run_phase2_processing(
            args.catalog, 
            test_mode=args.test, 
            test_count=args.count
        )
        
        if success:
            print("\n🎉 Phase 2 completed successfully!")
            print("📋 Ready for Phase 3: Model Training and Generation")
            return 0
        else:
            print("\n❌ Phase 2 failed!")
            return 1
            
    except KeyboardInterrupt:
        print("\n⏹️  Processing interrupted by user")
        return 1
    except Exception as e:
        logger.error(f"Unexpected error: {e}", exc_info=True)
        print(f"\n💥 Unexpected error: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
