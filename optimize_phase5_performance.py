#!/usr/bin/env python3
"""
Phase 5 Performance Optimization and Resource Management

Comprehensive performance analysis and optimization for RTX 3070 hardware,
including memory usage patterns, dynamic resource allocation, and bottleneck identification.
"""

import sys
import torch
import time
import psutil
import numpy as np
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.phase5 import (
    Phase5TrainingConfig, create_phase5_config, validate_hardware_compatibility
)
from src.model.tja_generator import TJAGeneratorModel
from src.utils.memory_monitor import MemoryMonitor


class Phase5PerformanceOptimizer:
    """
    Comprehensive performance optimizer for Phase 5 training system
    
    Analyzes memory usage patterns, optimizes resource allocation,
    and provides recommendations for RTX 3070 hardware.
    """
    
    def __init__(self):
        self.memory_monitor = MemoryMonitor()
        self.optimization_results = {}
        
        print("🔧 Phase 5 Performance Optimizer")
        print("=" * 50)
    
    def analyze_memory_patterns(self) -> Dict[str, Any]:
        """Analyze current memory usage patterns"""
        print("\n💾 Memory Pattern Analysis")
        print("-" * 30)
        
        # Get baseline memory stats
        baseline_stats = self.memory_monitor.get_memory_stats()
        
        print(f"📊 Baseline Memory Status:")
        print(f"   GPU Total: {baseline_stats.gpu_total_gb:.2f}GB")
        print(f"   GPU Reserved: {baseline_stats.gpu_reserved_gb:.2f}GB")
        print(f"   GPU Available: {baseline_stats.gpu_total_gb - baseline_stats.gpu_reserved_gb:.2f}GB")
        print(f"   System Memory: {baseline_stats.system_memory_percent:.1f}%")
        
        # Test different configurations
        memory_tests = []
        
        # Test 1: Small batch size
        config_small = create_phase5_config(batch_size=1)
        memory_usage_small = self._estimate_memory_usage(config_small)
        memory_tests.append(("Small Batch (1)", memory_usage_small))
        
        # Test 2: Medium batch size (current default)
        config_medium = create_phase5_config(batch_size=2)
        memory_usage_medium = self._estimate_memory_usage(config_medium)
        memory_tests.append(("Medium Batch (2)", memory_usage_medium))
        
        # Test 3: Large batch size
        config_large = create_phase5_config(batch_size=4)
        memory_usage_large = self._estimate_memory_usage(config_large)
        memory_tests.append(("Large Batch (4)", memory_usage_large))
        
        print(f"\n📈 Memory Usage Estimates:")
        for name, usage in memory_tests:
            total_gb = usage["total_estimated_gb"]
            utilization = (total_gb / baseline_stats.gpu_total_gb) * 100
            status = "✅ SAFE" if total_gb < 6.8 else "⚠️ RISKY" if total_gb < 7.5 else "❌ UNSAFE"
            
            print(f"   {name:<15}: {total_gb:.2f}GB ({utilization:.1f}%) {status}")
        
        # Find optimal configuration
        safe_configs = [(name, usage) for name, usage in memory_tests 
                       if usage["total_estimated_gb"] < 6.8]
        
        if safe_configs:
            optimal_config = max(safe_configs, key=lambda x: x[1]["total_estimated_gb"])
            print(f"\n🎯 Optimal Configuration: {optimal_config[0]}")
        else:
            print(f"\n⚠️ All configurations exceed safe memory limits")
        
        return {
            "baseline_stats": {
                "gpu_allocated_gb": baseline_stats.gpu_allocated_gb,
                "gpu_reserved_gb": baseline_stats.gpu_reserved_gb,
                "gpu_free_gb": baseline_stats.gpu_free_gb,
                "gpu_total_gb": baseline_stats.gpu_total_gb,
                "gpu_utilization_percent": baseline_stats.gpu_utilization_percent,
                "system_memory_gb": baseline_stats.system_memory_gb,
                "system_memory_percent": baseline_stats.system_memory_percent,
                "system_available_gb": baseline_stats.system_available_gb
            },
            "memory_tests": {name: usage for name, usage in memory_tests},
            "optimal_config": optimal_config[0] if safe_configs else None
        }
    
    def _estimate_memory_usage(self, config: Phase5TrainingConfig) -> Dict[str, float]:
        """Estimate memory usage for a given configuration"""
        # Model parameters (1.56M parameters)
        model_params = 1.56e6
        model_memory_gb = model_params * 4 / (1024**3)  # 4 bytes per float32
        
        # Batch memory calculation
        batch_size = config.batch_size
        seq_len = 400  # Maximum sequence length
        feature_dim = 201  # Audio feature dimensions
        
        # Audio features memory
        audio_memory = batch_size * seq_len * feature_dim * 4 / (1024**3)
        
        # Note sequences memory
        note_memory = batch_size * seq_len * 4 / (1024**3)
        
        # Timing sequences memory
        timing_memory = batch_size * seq_len * 4 / (1024**3)
        
        # Total batch memory
        batch_memory_gb = audio_memory + note_memory + timing_memory
        
        # Gradient memory (optimizer states)
        gradient_memory_gb = model_memory_gb * 2  # Gradients + optimizer states
        
        # Activation memory (rough estimate)
        activation_memory_gb = batch_memory_gb * 8  # Rough estimate for transformer activations
        
        # Mixed precision reduction
        if config.mixed_precision:
            activation_memory_gb *= 0.6  # ~40% reduction with FP16
        
        # Gradient checkpointing reduction
        if config.gradient_checkpointing:
            activation_memory_gb *= 0.5  # ~50% reduction with checkpointing
        
        # Total estimated memory
        total_estimated_gb = (model_memory_gb + batch_memory_gb + 
                            gradient_memory_gb + activation_memory_gb)
        
        return {
            "model_memory_gb": model_memory_gb,
            "batch_memory_gb": batch_memory_gb,
            "gradient_memory_gb": gradient_memory_gb,
            "activation_memory_gb": activation_memory_gb,
            "total_estimated_gb": total_estimated_gb
        }
    
    def optimize_dynamic_resource_allocation(self) -> Dict[str, Any]:
        """Implement dynamic resource allocation optimization"""
        print("\n🔄 Dynamic Resource Allocation Optimization")
        print("-" * 45)
        
        # Get current GPU memory status
        current_stats = self.memory_monitor.get_memory_stats()
        available_memory_gb = current_stats.gpu_total_gb - current_stats.gpu_reserved_gb
        
        print(f"📊 Available GPU Memory: {available_memory_gb:.2f}GB")
        
        # Calculate optimal batch size based on available memory
        optimal_batch_size = self._calculate_optimal_batch_size(available_memory_gb)
        
        # Calculate optimal gradient accumulation
        target_effective_batch_size = 16  # Target effective batch size
        optimal_grad_accum = max(1, target_effective_batch_size // optimal_batch_size)
        
        # Calculate optimal number of workers
        cpu_cores = psutil.cpu_count(logical=False)
        optimal_workers = min(4, max(1, cpu_cores // 2))  # Conservative for GPU training
        
        optimization_recommendations = {
            "optimal_batch_size": optimal_batch_size,
            "optimal_gradient_accumulation": optimal_grad_accum,
            "optimal_workers": optimal_workers,
            "effective_batch_size": optimal_batch_size * optimal_grad_accum,
            "memory_utilization_target": 0.85,  # 85% of available memory
            "safety_margin_gb": 1.2  # 1.2GB safety margin
        }
        
        print(f"🎯 Optimization Recommendations:")
        print(f"   Batch Size: {optimal_batch_size}")
        print(f"   Gradient Accumulation: {optimal_grad_accum}")
        print(f"   Effective Batch Size: {optimal_batch_size * optimal_grad_accum}")
        print(f"   Data Workers: {optimal_workers}")
        print(f"   Memory Target: {optimization_recommendations['memory_utilization_target']*100:.0f}%")
        
        return optimization_recommendations
    
    def _calculate_optimal_batch_size(self, available_memory_gb: float) -> int:
        """Calculate optimal batch size based on available memory"""
        # Reserve safety margin
        usable_memory_gb = available_memory_gb - 1.2  # 1.2GB safety margin
        
        # Test different batch sizes
        for batch_size in [8, 4, 2, 1]:
            config = create_phase5_config(batch_size=batch_size)
            memory_usage = self._estimate_memory_usage(config)
            
            if memory_usage["total_estimated_gb"] <= usable_memory_gb:
                return batch_size
        
        return 1  # Fallback to minimum batch size
    
    def profile_training_performance(self) -> Dict[str, Any]:
        """Profile training performance and identify bottlenecks"""
        print("\n📊 Training Performance Profiling")
        print("-" * 35)
        
        # Create model and test data
        config = create_phase5_config(batch_size=2)
        model = TJAGeneratorModel(config.get_model_config())
        
        if torch.cuda.is_available():
            model = model.cuda()
        
        # Create test batch
        batch_size = 2
        seq_len = 100  # Shorter for profiling
        feature_dim = 201
        
        audio_features = torch.randn(batch_size, seq_len, feature_dim)
        note_sequence = torch.randint(0, 8, (batch_size, seq_len))
        timing_sequence = torch.randint(0, 64, (batch_size, seq_len))  # Integer timing tokens
        difficulty = torch.randint(0, 3, (batch_size,))
        
        if torch.cuda.is_available():
            audio_features = audio_features.cuda()
            note_sequence = note_sequence.cuda()
            timing_sequence = timing_sequence.cuda()
            difficulty = difficulty.cuda()
        
        # Profile forward pass
        print("🔍 Profiling forward pass...")
        forward_times = []
        
        for i in range(10):  # 10 iterations for averaging
            start_time = time.time()
            
            with torch.no_grad():
                outputs = model(
                    audio_features=audio_features,
                    target_sequence=note_sequence,
                    target_timing=timing_sequence,
                    difficulty=difficulty,
                    return_loss=True
                )
            
            if torch.cuda.is_available():
                torch.cuda.synchronize()
            
            forward_time = time.time() - start_time
            forward_times.append(forward_time)
        
        # Profile backward pass
        print("🔍 Profiling backward pass...")
        backward_times = []
        
        model.train()
        optimizer = torch.optim.AdamW(model.parameters(), lr=1e-4)
        
        for i in range(10):
            optimizer.zero_grad()
            
            start_time = time.time()
            
            outputs = model(
                audio_features=audio_features,
                target_sequence=note_sequence,
                target_timing=timing_sequence,
                difficulty=difficulty,
                return_loss=True
            )
            
            # Handle different output formats
            if "losses" in outputs:
                loss = sum(outputs["losses"].values())
            elif "loss" in outputs:
                loss = outputs["loss"]
            else:
                # Create a dummy loss from logits
                logits = outputs.get("logits", torch.randn(batch_size, seq_len, 8).cuda())
                loss = torch.nn.functional.cross_entropy(
                    logits.view(-1, logits.size(-1)),
                    note_sequence.view(-1)
                )

            loss.backward()
            
            if torch.cuda.is_available():
                torch.cuda.synchronize()
            
            backward_time = time.time() - start_time
            backward_times.append(backward_time)
        
        # Calculate statistics
        avg_forward_time = np.mean(forward_times)
        avg_backward_time = np.mean(backward_times)
        total_step_time = avg_forward_time + avg_backward_time
        
        # Calculate throughput
        samples_per_second = batch_size / total_step_time
        
        profiling_results = {
            "forward_pass": {
                "avg_time_ms": avg_forward_time * 1000,
                "std_time_ms": np.std(forward_times) * 1000,
                "min_time_ms": np.min(forward_times) * 1000,
                "max_time_ms": np.max(forward_times) * 1000
            },
            "backward_pass": {
                "avg_time_ms": avg_backward_time * 1000,
                "std_time_ms": np.std(backward_times) * 1000,
                "min_time_ms": np.min(backward_times) * 1000,
                "max_time_ms": np.max(backward_times) * 1000
            },
            "total_step_time_ms": total_step_time * 1000,
            "throughput_samples_per_sec": samples_per_second,
            "memory_usage_gb": self.memory_monitor.get_memory_stats().gpu_reserved_gb
        }
        
        print(f"📈 Performance Results:")
        print(f"   Forward Pass: {avg_forward_time*1000:.1f}ms")
        print(f"   Backward Pass: {avg_backward_time*1000:.1f}ms")
        print(f"   Total Step: {total_step_time*1000:.1f}ms")
        print(f"   Throughput: {samples_per_second:.1f} samples/sec")
        print(f"   GPU Memory: {profiling_results['memory_usage_gb']:.2f}GB")
        
        return profiling_results
    
    def generate_optimization_recommendations(self) -> List[str]:
        """Generate specific optimization recommendations"""
        print("\n💡 Optimization Recommendations")
        print("-" * 35)
        
        recommendations = []
        
        # Memory-based recommendations
        memory_stats = self.memory_monitor.get_memory_stats()
        memory_utilization = memory_stats.gpu_reserved_gb / memory_stats.gpu_total_gb
        
        if memory_utilization < 0.5:
            recommendations.append("Low GPU memory utilization - consider increasing batch size")
        elif memory_utilization > 0.9:
            recommendations.append("High GPU memory utilization - consider reducing batch size or enabling gradient checkpointing")
        
        # CPU-based recommendations
        cpu_percent = psutil.cpu_percent(interval=1)
        if cpu_percent < 50:
            recommendations.append("Low CPU utilization - consider increasing data loading workers")
        elif cpu_percent > 90:
            recommendations.append("High CPU utilization - consider reducing data loading workers")
        
        # Hardware-specific recommendations
        if torch.cuda.is_available():
            gpu_props = torch.cuda.get_device_properties(0)
            if gpu_props.major >= 8:  # Ampere or newer
                recommendations.append("Modern GPU detected - enable Tensor Core optimizations with mixed precision")
            
            if gpu_props.total_memory / (1024**3) >= 10:  # 10GB+ VRAM
                recommendations.append("High VRAM GPU - consider increasing batch size or sequence length")
        
        # Training-specific recommendations
        recommendations.extend([
            "Enable gradient checkpointing to reduce memory usage",
            "Use mixed precision training for faster computation",
            "Implement dynamic loss scaling for numerical stability",
            "Consider using fused optimizers for better performance",
            "Enable model compilation with PyTorch 2.0 for speedup"
        ])
        
        for i, rec in enumerate(recommendations, 1):
            print(f"   {i}. {rec}")
        
        return recommendations
    
    def run_comprehensive_optimization(self) -> Dict[str, Any]:
        """Run comprehensive performance optimization analysis"""
        print("🚀 Running Comprehensive Performance Optimization")
        print("=" * 55)
        
        start_time = time.time()
        
        # Run all optimization analyses
        memory_analysis = self.analyze_memory_patterns()
        resource_optimization = self.optimize_dynamic_resource_allocation()
        performance_profiling = self.profile_training_performance()
        recommendations = self.generate_optimization_recommendations()
        
        execution_time = time.time() - start_time
        
        # Compile comprehensive results
        optimization_results = {
            "execution_info": {
                "start_time": time.strftime("%Y-%m-%dT%H:%M:%SZ", time.gmtime(start_time)),
                "execution_time_seconds": execution_time,
                "hardware_target": "NVIDIA RTX 3070"
            },
            "memory_analysis": memory_analysis,
            "resource_optimization": resource_optimization,
            "performance_profiling": performance_profiling,
            "recommendations": recommendations,
            "optimization_summary": {
                "optimal_batch_size": resource_optimization["optimal_batch_size"],
                "optimal_gradient_accumulation": resource_optimization["optimal_gradient_accumulation"],
                "effective_batch_size": resource_optimization["effective_batch_size"],
                "estimated_throughput": performance_profiling["throughput_samples_per_sec"],
                "memory_efficiency": memory_analysis["baseline_stats"]["gpu_reserved_gb"] / memory_analysis["baseline_stats"]["gpu_total_gb"] * 100
            }
        }
        
        print(f"\n🎯 Optimization Summary:")
        print(f"   Execution Time: {execution_time:.2f}s")
        print(f"   Optimal Batch Size: {resource_optimization['optimal_batch_size']}")
        print(f"   Effective Batch Size: {resource_optimization['effective_batch_size']}")
        print(f"   Estimated Throughput: {performance_profiling['throughput_samples_per_sec']:.1f} samples/sec")
        print(f"   Memory Efficiency: {optimization_results['optimization_summary']['memory_efficiency']:.1f}%")
        
        return optimization_results


def main():
    """Main optimization execution"""
    optimizer = Phase5PerformanceOptimizer()
    
    try:
        # Run comprehensive optimization
        results = optimizer.run_comprehensive_optimization()
        
        # Save results
        output_dir = Path("outputs/phase5_optimization")
        output_dir.mkdir(parents=True, exist_ok=True)
        
        import json
        results_file = output_dir / "performance_optimization_results.json"
        with open(results_file, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"\n📄 Results saved: {results_file}")
        print("✅ Phase 5 performance optimization completed successfully!")
        
        return True
        
    except Exception as e:
        print(f"❌ Optimization failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
