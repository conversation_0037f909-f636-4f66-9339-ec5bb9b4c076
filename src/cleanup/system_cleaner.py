"""
System Cleanup and Optimization

Comprehensive cleanup system to remove redundant files, optimize directory structure,
and implement final system optimizations.
"""

import os
import shutil
import logging
from pathlib import Path
from typing import Dict, List, Optional, Set, Tuple
from dataclasses import dataclass
import time

from ..paths.path_manager import PathManager, PathType
from ..config.unified_config_manager import UnifiedConfigManager


@dataclass
class CleanupReport:
    """Report of cleanup operations"""
    files_removed: int = 0
    directories_removed: int = 0
    bytes_freed: int = 0
    redundant_files: List[str] = None
    obsolete_directories: List[str] = None
    optimization_actions: List[str] = None
    cleanup_time_seconds: float = 0.0
    
    def __post_init__(self):
        if self.redundant_files is None:
            self.redundant_files = []
        if self.obsolete_directories is None:
            self.obsolete_directories = []
        if self.optimization_actions is None:
            self.optimization_actions = []


class SystemCleaner:
    """
    Comprehensive system cleanup and optimization
    
    Removes redundant files, cleans up documentation, optimizes directory structure,
    and implements final system optimizations.
    """
    
    def __init__(self, workspace_root: Optional[str] = None):
        self.logger = logging.getLogger(f"{__name__}.SystemCleaner")
        
        # Initialize path manager
        self.path_manager = PathManager(workspace_root)
        self.config_manager = UnifiedConfigManager()
        
        # Define cleanup patterns
        self.redundant_file_patterns = {
            # Test and temporary files
            "*.tmp", "*.temp", "*.bak", "*.backup", "*.old",
            "test_*.py", "*_test.py", "temp_*", "tmp_*",
            
            # Log files (keep recent ones)
            "*.log", "*.log.*",
            
            # Cache files
            "__pycache__", "*.pyc", "*.pyo", ".pytest_cache",
            
            # IDE and editor files
            ".vscode", ".idea", "*.swp", "*.swo", "*~",
            
            # OS files
            ".DS_Store", "Thumbs.db", "desktop.ini",
            
            # Documentation drafts
            "*_draft.md", "*_old.md", "*_backup.md"
        }
        
        # Directories to clean up
        self.cleanup_directories = [
            "wandb",  # Weights & Biases logs
            "outputs/test",  # Test outputs
            "temp",  # Temporary files
            "cache",  # Cache files
            ".pytest_cache",  # Pytest cache
            "__pycache__",  # Python cache
        ]
        
        # Essential files to preserve
        self.essential_files = {
            "README.md",
            "requirements.txt",
            "main_phase*.py",
            "FINAL_PRODUCTION_SUMMARY.md",
            "RESOURCE_OPTIMIZATION_REPORT.md"
        }
        
        self.logger.info("SystemCleaner initialized")
    
    def run_comprehensive_cleanup(self, dry_run: bool = False) -> CleanupReport:
        """
        Run comprehensive system cleanup
        
        Args:
            dry_run: If True, only report what would be cleaned without actually doing it
            
        Returns:
            CleanupReport with cleanup statistics
        """
        start_time = time.time()
        report = CleanupReport()
        
        self.logger.info(f"Starting comprehensive cleanup (dry_run={dry_run})")
        
        # Phase 1: Remove redundant files
        self._cleanup_redundant_files(report, dry_run)
        
        # Phase 2: Clean up obsolete directories
        self._cleanup_obsolete_directories(report, dry_run)
        
        # Phase 3: Optimize documentation
        self._optimize_documentation(report, dry_run)
        
        # Phase 4: Clean up test outputs
        self._cleanup_test_outputs(report, dry_run)
        
        # Phase 5: Optimize log files
        self._optimize_log_files(report, dry_run)
        
        # Phase 6: Final optimizations
        self._apply_final_optimizations(report, dry_run)
        
        report.cleanup_time_seconds = time.time() - start_time
        
        self.logger.info(f"Cleanup completed in {report.cleanup_time_seconds:.2f}s")
        self.logger.info(f"Files removed: {report.files_removed}")
        self.logger.info(f"Directories removed: {report.directories_removed}")
        self.logger.info(f"Space freed: {report.bytes_freed / (1024*1024):.1f}MB")
        
        return report
    
    def _cleanup_redundant_files(self, report: CleanupReport, dry_run: bool):
        """Remove redundant files based on patterns"""
        self.logger.info("Cleaning up redundant files...")
        
        workspace = self.path_manager.workspace_root
        
        for pattern in self.redundant_file_patterns:
            if pattern.startswith("*"):
                # Glob pattern
                files = list(workspace.rglob(pattern))
            else:
                # Directory pattern
                files = list(workspace.rglob(f"**/{pattern}"))
            
            for file_path in files:
                if self._should_preserve_file(file_path):
                    continue
                
                try:
                    if file_path.is_file():
                        size = file_path.stat().st_size
                        report.redundant_files.append(str(file_path))
                        
                        if not dry_run:
                            file_path.unlink()
                            report.files_removed += 1
                            report.bytes_freed += size
                            
                    elif file_path.is_dir() and pattern in ["__pycache__", ".pytest_cache"]:
                        size = self._get_directory_size(file_path)
                        report.obsolete_directories.append(str(file_path))
                        
                        if not dry_run:
                            shutil.rmtree(file_path)
                            report.directories_removed += 1
                            report.bytes_freed += size
                            
                except Exception as e:
                    self.logger.warning(f"Could not remove {file_path}: {e}")
    
    def _cleanup_obsolete_directories(self, report: CleanupReport, dry_run: bool):
        """Remove obsolete directories"""
        self.logger.info("Cleaning up obsolete directories...")
        
        workspace = self.path_manager.workspace_root
        
        for dir_name in self.cleanup_directories:
            dir_path = workspace / dir_name
            
            if dir_path.exists() and dir_path.is_dir():
                try:
                    size = self._get_directory_size(dir_path)
                    report.obsolete_directories.append(str(dir_path))
                    
                    if not dry_run:
                        shutil.rmtree(dir_path)
                        report.directories_removed += 1
                        report.bytes_freed += size
                        
                except Exception as e:
                    self.logger.warning(f"Could not remove directory {dir_path}: {e}")
    
    def _optimize_documentation(self, report: CleanupReport, dry_run: bool):
        """Optimize documentation by removing excessive files"""
        self.logger.info("Optimizing documentation...")
        
        docs_dir = self.path_manager.workspace_root / "docs"
        
        if not docs_dir.exists():
            return
        
        # Keep only essential documentation
        essential_docs = {
            "README.md",
            "API.md",
            "INSTALLATION.md",
            "USAGE.md"
        }
        
        for doc_file in docs_dir.rglob("*.md"):
            if doc_file.name not in essential_docs:
                # Check if it's a draft or backup
                if any(keyword in doc_file.name.lower() 
                      for keyword in ["draft", "backup", "old", "temp"]):
                    
                    size = doc_file.stat().st_size
                    report.redundant_files.append(str(doc_file))
                    
                    if not dry_run:
                        doc_file.unlink()
                        report.files_removed += 1
                        report.bytes_freed += size
        
        report.optimization_actions.append("Optimized documentation structure")
    
    def _cleanup_test_outputs(self, report: CleanupReport, dry_run: bool):
        """Clean up test output files"""
        self.logger.info("Cleaning up test outputs...")
        
        # Clean up test data directories
        test_patterns = [
            "data/test",
            "outputs/test",
            "temp/test",
            "test_output*",
            "validation_report.json"
        ]
        
        workspace = self.path_manager.workspace_root
        
        for pattern in test_patterns:
            paths = list(workspace.rglob(pattern))
            
            for path in paths:
                try:
                    if path.is_file():
                        size = path.stat().st_size
                        report.redundant_files.append(str(path))
                        
                        if not dry_run:
                            path.unlink()
                            report.files_removed += 1
                            report.bytes_freed += size
                            
                    elif path.is_dir():
                        size = self._get_directory_size(path)
                        report.obsolete_directories.append(str(path))
                        
                        if not dry_run:
                            shutil.rmtree(path)
                            report.directories_removed += 1
                            report.bytes_freed += size
                            
                except Exception as e:
                    self.logger.warning(f"Could not remove test output {path}: {e}")
        
        report.optimization_actions.append("Cleaned up test outputs")
    
    def _optimize_log_files(self, report: CleanupReport, dry_run: bool):
        """Optimize log files by keeping only recent ones"""
        self.logger.info("Optimizing log files...")
        
        logs_dir = self.path_manager.get_standardized_path(PathType.LOGS)
        
        if not logs_dir.exists():
            return
        
        # Keep only logs from last 7 days
        cutoff_time = time.time() - (7 * 24 * 3600)  # 7 days ago
        
        for log_file in logs_dir.rglob("*.log*"):
            try:
                if log_file.stat().st_mtime < cutoff_time:
                    size = log_file.stat().st_size
                    report.redundant_files.append(str(log_file))
                    
                    if not dry_run:
                        log_file.unlink()
                        report.files_removed += 1
                        report.bytes_freed += size
                        
            except Exception as e:
                self.logger.warning(f"Could not process log file {log_file}: {e}")
        
        report.optimization_actions.append("Optimized log file retention")
    
    def _apply_final_optimizations(self, report: CleanupReport, dry_run: bool):
        """Apply final system optimizations"""
        self.logger.info("Applying final optimizations...")
        
        # Clean up temporary directories
        temp_dir = self.path_manager.get_standardized_path(PathType.TEMP)
        if temp_dir.exists():
            self.path_manager.cleanup_temp_files(max_age_hours=1)
        
        # Optimize cache directory
        cache_dir = self.path_manager.get_standardized_path(PathType.CACHE)
        if cache_dir.exists():
            try:
                size = self._get_directory_size(cache_dir)
                if size > 100 * 1024 * 1024:  # > 100MB
                    report.obsolete_directories.append(str(cache_dir))
                    
                    if not dry_run:
                        shutil.rmtree(cache_dir)
                        cache_dir.mkdir(parents=True, exist_ok=True)
                        report.directories_removed += 1
                        report.bytes_freed += size
                        
            except Exception as e:
                self.logger.warning(f"Could not optimize cache directory: {e}")
        
        report.optimization_actions.extend([
            "Cleaned temporary files",
            "Optimized cache directory",
            "Applied final system optimizations"
        ])
    
    def _should_preserve_file(self, file_path: Path) -> bool:
        """Check if file should be preserved"""
        # Preserve essential files
        if file_path.name in self.essential_files:
            return True
        
        # Preserve files in src directory (core code)
        if "src" in file_path.parts:
            return True
        
        # Preserve main entry points
        if file_path.name.startswith("main_") and file_path.suffix == ".py":
            return True
        
        # Preserve configuration files
        if file_path.suffix in [".json", ".yaml", ".yml"] and "config" in file_path.name.lower():
            return True
        
        return False
    
    def _get_directory_size(self, directory: Path) -> int:
        """Get total size of directory in bytes"""
        total_size = 0
        try:
            for file_path in directory.rglob("*"):
                if file_path.is_file():
                    total_size += file_path.stat().st_size
        except Exception:
            pass
        return total_size
    
    def generate_cleanup_report(self, report: CleanupReport, output_path: Optional[str] = None) -> str:
        """Generate detailed cleanup report"""
        report_content = f"""# System Cleanup Report

**Cleanup completed:** {time.strftime('%Y-%m-%d %H:%M:%S')}
**Processing time:** {report.cleanup_time_seconds:.2f} seconds

## Summary
- **Files removed:** {report.files_removed}
- **Directories removed:** {report.directories_removed}
- **Space freed:** {report.bytes_freed / (1024*1024):.1f} MB

## Optimization Actions
"""
        
        for action in report.optimization_actions:
            report_content += f"- {action}\n"
        
        report_content += f"""
## Files Removed ({len(report.redundant_files)})
"""
        
        for file_path in report.redundant_files[:20]:  # Show first 20
            report_content += f"- {file_path}\n"
        
        if len(report.redundant_files) > 20:
            report_content += f"- ... and {len(report.redundant_files) - 20} more files\n"
        
        report_content += f"""
## Directories Removed ({len(report.obsolete_directories)})
"""
        
        for dir_path in report.obsolete_directories:
            report_content += f"- {dir_path}\n"
        
        # Save report if output path specified
        if output_path:
            try:
                with open(output_path, 'w', encoding='utf-8') as f:
                    f.write(report_content)
                self.logger.info(f"Cleanup report saved to {output_path}")
            except Exception as e:
                self.logger.error(f"Could not save cleanup report: {e}")
        
        return report_content
