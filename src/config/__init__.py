"""
Configuration Management for TJA Generator

Centralized configuration system with hardware optimization,
consistent path handling, and phase-specific settings.
"""

from .unified_config_manager import (
    UnifiedConfigManager,
    HardwareConfig,
    PathConfig,
    ProcessingConfig,
    LoggingConfig
)

__all__ = [
    'UnifiedConfigManager',
    'HardwareConfig',
    'PathConfig', 
    'ProcessingConfig',
    'LoggingConfig'
]
