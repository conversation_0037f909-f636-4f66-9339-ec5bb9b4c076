#!/usr/bin/env python3
"""
Phase 4: TJA Generator Integration and Deployment

Main entry point for the complete TJA generation system.
Provides CLI interface, API server, and deployment validation.
"""

import sys
import argparse
import logging
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.phase4 import (
    TJAGenerationPipeline, 
    TJAGenerationAPI, 
    TJAGenerationCLI,
    DeploymentManager,
    PHASE_4_CONFIG
)


def setup_logging(verbose: bool = False):
    """Setup main logging"""
    level = logging.DEBUG if verbose else logging.INFO
    
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('logs/phase4_main.log')
        ]
    )
    
    # Create logs directory
    Path('logs').mkdir(exist_ok=True)


def create_argument_parser() -> argparse.ArgumentParser:
    """Create main argument parser"""
    parser = argparse.ArgumentParser(
        description="TJA Generator v1.0 - Complete TJA Generation System",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Available Commands:
  cli       - Run command-line interface for batch processing
  api       - Start REST API server
  pipeline  - Run single-file pipeline processing
  deploy    - Run deployment validation and setup
  test      - Run comprehensive system tests

Examples:
  # Run CLI for single file
  python main_phase4.py cli audio.mp3
  
  # Start API server
  python main_phase4.py api --host 0.0.0.0 --port 8000
  
  # Run deployment validation
  python main_phase4.py deploy --validate
  
  # Run system tests
  python main_phase4.py test --comprehensive
        """
    )
    
    parser.add_argument(
        'command',
        choices=['cli', 'api', 'pipeline', 'deploy', 'test'],
        help='Command to execute'
    )
    
    parser.add_argument(
        '-v', '--verbose',
        action='store_true',
        help='Enable verbose logging'
    )
    
    parser.add_argument(
        '--config',
        help='Path to custom configuration file'
    )
    
    # CLI-specific arguments
    parser.add_argument(
        'input_file',
        nargs='?',
        help='Input audio file (for cli and pipeline commands)'
    )
    
    parser.add_argument(
        '-o', '--output',
        help='Output file path'
    )
    
    parser.add_argument(
        '-d', '--difficulties',
        nargs='+',
        type=int,
        choices=[8, 9, 10],
        default=[8, 9, 10],
        help='Difficulty levels to generate'
    )
    
    # API-specific arguments
    parser.add_argument(
        '--host',
        default='localhost',
        help='API server host'
    )
    
    parser.add_argument(
        '--port',
        type=int,
        default=8000,
        help='API server port'
    )
    
    # Deployment arguments
    parser.add_argument(
        '--validate',
        action='store_true',
        help='Run deployment validation'
    )
    
    parser.add_argument(
        '--benchmark',
        action='store_true',
        help='Run performance benchmarks'
    )
    
    parser.add_argument(
        '--report',
        action='store_true',
        help='Generate deployment report'
    )
    
    # Test arguments
    parser.add_argument(
        '--comprehensive',
        action='store_true',
        help='Run comprehensive tests'
    )
    
    parser.add_argument(
        '--unit-tests',
        action='store_true',
        help='Run unit tests only'
    )
    
    parser.add_argument(
        '--integration-tests',
        action='store_true',
        help='Run integration tests only'
    )
    
    return parser


def run_cli_command(args: argparse.Namespace) -> int:
    """Run CLI command"""
    if not args.input_file:
        print("Error: input_file required for CLI command")
        return 1
    
    # Prepare CLI arguments
    cli_args = [args.input_file]
    
    if args.output:
        cli_args.extend(['-o', args.output])
    
    if args.difficulties:
        cli_args.extend(['-d'] + [str(d) for d in args.difficulties])
    
    if args.verbose:
        cli_args.append('-v')
    
    # Run CLI
    cli = TJAGenerationCLI()
    return cli.run(cli_args)


def run_api_command(args: argparse.Namespace) -> int:
    """Run API server command"""
    try:
        api = TJAGenerationAPI()
        print(f"Starting TJA Generator API server on {args.host}:{args.port}")
        print(f"API documentation available at: http://{args.host}:{args.port}/docs")
        
        api.run(host=args.host, port=args.port)
        return 0
        
    except ImportError:
        print("Error: FastAPI not installed. Install with: pip install fastapi uvicorn")
        return 1
    except Exception as e:
        print(f"Error starting API server: {e}")
        return 1


def run_pipeline_command(args: argparse.Namespace) -> int:
    """Run single pipeline command"""
    if not args.input_file:
        print("Error: input_file required for pipeline command")
        return 1
    
    try:
        print(f"Processing {args.input_file} with TJA Generation Pipeline...")
        
        pipeline = TJAGenerationPipeline()
        
        if not pipeline.initialize():
            print("Error: Pipeline initialization failed")
            return 1
        
        result = pipeline.generate_tja(
            audio_file_path=args.input_file,
            difficulty_levels=args.difficulties,
            output_path=args.output
        )
        
        if result["success"]:
            print(f"✅ TJA generated successfully: {result['output_file_path']}")
            print(f"Processing time: {result['processing_time_seconds']:.2f} seconds")
            
            if "quality_metrics" in result:
                overall_quality = result["quality_metrics"].get("overall_quality", 0.0)
                print(f"Quality score: {overall_quality:.3f}")
            
            return 0
        else:
            print("❌ TJA generation failed")
            return 1
            
    except Exception as e:
        print(f"Error: {e}")
        return 1


def run_deploy_command(args: argparse.Namespace) -> int:
    """Run deployment command"""
    try:
        deployment_manager = DeploymentManager()
        
        if args.validate:
            print("Running deployment validation...")
            validation_results = deployment_manager.validate_deployment()
            
            print(f"\nDeployment Status: {validation_results['overall_status'].upper()}")
            
            if validation_results['overall_status'] in ['fail', 'error']:
                print("❌ Deployment validation failed")
                return 1
            elif validation_results['overall_status'] == 'warning':
                print("⚠️  Deployment validation passed with warnings")
            else:
                print("✅ Deployment validation successful")
        
        if args.benchmark:
            print("Running performance benchmarks...")
            benchmark_results = deployment_manager.run_performance_benchmark()
            
            print(f"\nBenchmark Status: {benchmark_results['overall_status'].upper()}")
            
            for name, result in benchmark_results["benchmarks"].items():
                status_symbol = {"pass": "✅", "warning": "⚠️", "fail": "❌"}.get(result["status"], "❓")
                print(f"{status_symbol} {name}: {result['message']}")
        
        if args.report:
            print("Generating deployment report...")
            report_path = deployment_manager.save_deployment_report()
            print(f"📄 Deployment report saved: {report_path}")
        
        return 0
        
    except Exception as e:
        print(f"Error: {e}")
        return 1


def run_test_command(args: argparse.Namespace) -> int:
    """Run test command"""
    try:
        from test_phase4 import run_phase4_tests
        
        test_types = []
        
        if args.comprehensive:
            test_types = ["unit", "integration", "performance"]
        else:
            if args.unit_tests:
                test_types.append("unit")
            if args.integration_tests:
                test_types.append("integration")
            
            if not test_types:
                test_types = ["unit", "integration"]  # Default
        
        print(f"Running Phase 4 tests: {', '.join(test_types)}")
        
        success = run_phase4_tests(test_types, verbose=args.verbose)
        
        if success:
            print("✅ All tests passed")
            return 0
        else:
            print("❌ Some tests failed")
            return 1
            
    except ImportError:
        print("Error: Test module not found")
        return 1
    except Exception as e:
        print(f"Error running tests: {e}")
        return 1


def main() -> int:
    """Main entry point"""
    parser = create_argument_parser()
    args = parser.parse_args()
    
    # Setup logging
    setup_logging(args.verbose)
    
    # Load custom configuration if provided
    if args.config:
        print(f"Loading custom configuration: {args.config}")
        # TODO: Implement custom config loading
    
    # Route to appropriate command handler
    if args.command == 'cli':
        return run_cli_command(args)
    elif args.command == 'api':
        return run_api_command(args)
    elif args.command == 'pipeline':
        return run_pipeline_command(args)
    elif args.command == 'deploy':
        return run_deploy_command(args)
    elif args.command == 'test':
        return run_test_command(args)
    else:
        print(f"Unknown command: {args.command}")
        return 1


if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\nOperation cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"Unexpected error: {e}")
        sys.exit(1)
