#!/usr/bin/env python3
"""
Create Test Audio File

Generate a simple test audio file for Phase 4 demonstration
"""

import numpy as np
import wave
from pathlib import Path

def create_test_audio():
    """Create a simple test audio file"""
    # Audio parameters
    sample_rate = 44100
    duration = 10  # 10 seconds
    frequency = 440  # A4 note
    
    # Generate time array
    t = np.linspace(0, duration, int(sample_rate * duration))
    
    # Generate audio signal (simple sine wave with some variation)
    audio = np.sin(2 * np.pi * frequency * t)
    audio += 0.3 * np.sin(2 * np.pi * frequency * 2 * t)  # Add harmonic
    audio += 0.1 * np.sin(2 * np.pi * frequency * 0.5 * t)  # Add sub-harmonic
    
    # Add some rhythm variation
    beat_freq = 2.0  # 2 beats per second (120 BPM)
    beat_envelope = 0.5 + 0.5 * np.sin(2 * np.pi * beat_freq * t)
    audio *= beat_envelope
    
    # Normalize to 16-bit range
    audio = np.clip(audio, -1.0, 1.0)
    audio_int16 = (audio * 32767).astype(np.int16)
    
    # Create output directory
    output_dir = Path("data/test")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Save as WAV file
    output_path = output_dir / "test_audio.wav"
    
    with wave.open(str(output_path), 'w') as wav_file:
        wav_file.setnchannels(1)  # Mono
        wav_file.setsampwidth(2)  # 16-bit
        wav_file.setframerate(sample_rate)
        wav_file.writeframes(audio_int16.tobytes())
    
    print(f"✅ Test audio file created: {output_path}")
    print(f"   Duration: {duration} seconds")
    print(f"   Sample rate: {sample_rate} Hz")
    print(f"   Format: 16-bit mono WAV")
    
    return str(output_path)

if __name__ == "__main__":
    create_test_audio()
