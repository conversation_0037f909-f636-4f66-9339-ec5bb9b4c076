# Resource Management System

## Overview

The Phase 6 TJA Generator includes a comprehensive resource management system designed to operate efficiently within memory-constrained environments. This system provides real-time monitoring, dynamic allocation, and graceful degradation to ensure stable operation regardless of available system resources.

## Key Features

### 🔍 **Resource Detection**
- Automatic detection of system RAM, GPU memory, and CPU cores
- Dynamic configuration adjustment based on available resources
- Hardware-specific optimizations (RTX 3070 optimized)

### 📊 **Real-Time Monitoring**
- Continuous monitoring of system and GPU memory usage
- CPU utilization tracking
- Configurable alert thresholds with automatic cleanup
- Resource usage history and peak tracking

### ⚡ **Dynamic Allocation**
- Smart resource allocation with availability checking
- Automatic batch size adjustment based on memory constraints
- Memory-efficient configuration generation
- Resource deallocation with cleanup

### 🛡️ **Error Handling**
- Out-of-memory detection and recovery
- Graceful degradation with reduced settings
- Automatic retry with memory-efficient configurations
- Emergency cleanup procedures

## System Requirements

### Minimum Requirements
- **RAM**: 4GB (8GB recommended)
- **GPU**: Optional (CPU fallback available)
- **Storage**: 2GB free space
- **Python**: 3.8+

### Optimal Configuration
- **RAM**: 16GB+ for high-performance processing
- **GPU**: RTX 3070 or equivalent (6GB+ VRAM)
- **Storage**: 10GB+ for temporary files and models

## Usage

### 1. System Resource Detection

```bash
# Detect available system resources
python monitor_resources.py detect
```

**Output Example:**
```
🔍 System Resource Detection
========================================
💾 System Memory:
   Total: 16.0GB
   Available: 12.3GB
   Used: 23.1%
   ✅ Excellent memory capacity

🎮 GPU:
   Name: NVIDIA GeForce RTX 3070
   Memory: 8.0GB
   Compute: 8.6
   ✅ Optimal GPU for TJA Generator
```

### 2. Optimal Settings Generation

```bash
# Generate optimal settings for your system
python monitor_resources.py optimize
```

This creates `optimal_phase6_settings.json` with recommended configuration:

```json
{
  "max_system_memory_gb": 8.0,
  "max_gpu_memory_gb": 4.0,
  "batch_size": 2,
  "chunk_size": 2000,
  "use_gpu": true,
  "use_mixed_precision": true
}
```

### 3. Real-Time Monitoring

```bash
# Start real-time resource monitoring
python monitor_resources.py monitor --interval 1.0
```

**Live Display:**
```
🕒 2024-01-15 14:30:25
==================================================
💾 System Memory:
   Used: 6.2GB (38.8%)
   Available: 9.8GB
   Status: 🟢 GOOD

🎮 GPU Memory:
   Used: 2.1GB (26.3%)
   Available: 5.9GB
   Status: 🟢 GOOD

🖥️  CPU Usage: 45.2%
```

### 4. Phase 6 Inference with Resource Management

```bash
# Run inference with automatic resource management
python main_phase6.py inference audio.wav --bpm 140 --difficulty 9
```

**Resource-Aware Output:**
```
📁 Audio file: song.wav (25.3MB)
📊 Configuration:
   Memory limits: RAM 6.0GB, GPU 2.5GB
💾 Checking resource availability...
   ✅ Resources available (estimated need: 1.2GB RAM, 0.8GB GPU)
🔧 Initializing inference system...
```

## Configuration Options

### Resource Limits

```python
from src.phase6 import create_phase6_config

# Create configuration with custom resource limits
config = create_phase6_config(
    max_system_memory_gb=8.0,    # Maximum system RAM usage
    max_gpu_memory_gb=4.0,       # Maximum GPU VRAM usage
    debug_mode=False
)
```

### Memory Management Settings

```python
# Resource configuration options
resource_config = {
    "enable_memory_monitoring": True,
    "memory_warning_threshold": 0.75,    # 75% usage warning
    "memory_critical_threshold": 0.85,   # 85% usage critical
    "cleanup_interval_seconds": 30.0,
    "enable_dynamic_batch_sizing": True,
    "enable_emergency_cleanup": True,
    "oom_retry_attempts": 3,
    "oom_retry_delay_seconds": 5.0
}
```

## Memory Optimization Strategies

### 1. **Automatic Configuration Adjustment**

The system automatically adjusts settings based on available memory:

| Available RAM | Configuration |
|---------------|---------------|
| < 4GB | Ultra-low memory mode |
| 4-8GB | Memory-efficient mode |
| 8-16GB | Standard mode |
| 16GB+ | High-performance mode |

### 2. **Dynamic Batch Sizing**

```python
# Automatic batch size calculation
optimal_batch_size = resource_manager.get_optimal_batch_size(
    base_batch_size=8,
    memory_per_sample_mb=50.0
)
```

### 3. **Chunked Processing**

Large audio files are automatically processed in chunks:

```python
# Chunked inference settings
inference_config = {
    "use_chunked_inference": True,
    "max_chunk_size": 1000,      # Frames per chunk
    "chunk_overlap": 100         # Overlap between chunks
}
```

### 4. **Memory-Efficient Decorators**

```python
from src.utils.resource_manager import memory_efficient

@memory_efficient(memory_limit_gb=2.0)
def process_audio(audio_data):
    # Function automatically checks memory availability
    # and performs cleanup before/after execution
    return processed_data
```

## Error Handling

### Out-of-Memory Recovery

When memory limits are exceeded, the system:

1. **Detects** the out-of-memory condition
2. **Cleans up** allocated resources
3. **Retries** with reduced memory settings
4. **Falls back** to CPU-only processing if needed
5. **Provides** actionable recommendations

**Example Error Response:**
```json
{
  "success": false,
  "error_type": "MemoryError",
  "recommendations": [
    "Reduce audio file length",
    "Use CPU-only inference", 
    "Increase system memory",
    "Close other applications"
  ]
}
```

### Graceful Degradation

The system automatically applies increasingly conservative settings:

1. **First attempt**: Reduce chunk size by 50%
2. **Second attempt**: Switch to CPU, reduce chunk size further
3. **Final attempt**: Minimal settings (100 frame chunks, no optimizations)

## Testing

### Resource Management Test Suite

```bash
# Run comprehensive resource management tests
python test_resource_management.py
```

**Test Categories:**
- Resource Detection
- Memory Monitoring  
- Dynamic Allocation
- Memory Cleanup
- OOM Handling
- Configuration Adaptation
- Memory Efficient Decorators

### Stress Testing

```bash
# Run 60-second stress test
python test_resource_management.py --stress --duration 60
```

## Troubleshooting

### Common Issues

#### 1. **High Memory Usage**
```
⚠️  WARNING: High system memory usage 6.8GB (85.0%)
```
**Solutions:**
- Close unnecessary applications
- Use shorter audio files (< 3 minutes)
- Enable CPU-only mode
- Restart system to free memory

#### 2. **GPU Memory Insufficient**
```
❌ GPU memory insufficient (2.1GB < 4.0GB), using CPU
```
**Solutions:**
- Reduce `max_gpu_memory_gb` setting
- Enable `force_cpu_fallback`
- Close GPU-intensive applications
- Use smaller chunk sizes

#### 3. **Out of Memory During Inference**
```
💾 Out of memory: Insufficient resources for inference
```
**Solutions:**
- Use audio files < 2 minutes
- Increase available system memory
- Close other memory-intensive applications
- Use CPU-only inference

### Performance Optimization

#### For Low-Memory Systems (< 8GB RAM)
```python
config = create_phase6_config(
    max_system_memory_gb=3.0,
    max_gpu_memory_gb=1.0,
    debug_mode=False
)

# Additional optimizations
config.inference.max_chunk_size = 500
config.inference.use_mixed_precision = False
config.inference.force_cpu_fallback = True
```

#### For High-Memory Systems (16GB+ RAM)
```python
config = create_phase6_config(
    max_system_memory_gb=12.0,
    max_gpu_memory_gb=6.0,
    debug_mode=False
)

# Performance optimizations
config.inference.max_chunk_size = 2000
config.inference.use_mixed_precision = True
config.inference.compile_model = True
```

## Monitoring and Alerts

### Alert Thresholds

| Threshold | Action |
|-----------|--------|
| 75% memory usage | Warning logged |
| 85% memory usage | Emergency cleanup triggered |
| 95% memory usage | Process termination considered |

### Custom Alert Callbacks

```python
def memory_alert_callback(status):
    if status.system_memory_percent > 80:
        print(f"⚠️  High memory usage: {status.system_memory_percent:.1f}%")
        # Custom cleanup or notification logic

resource_manager.monitor.add_alert_callback(memory_alert_callback)
```

## Best Practices

1. **Always monitor resources** during long-running operations
2. **Use appropriate chunk sizes** based on available memory
3. **Enable automatic cleanup** between operations
4. **Test with your target hardware** before production use
5. **Monitor peak memory usage** to optimize settings
6. **Use CPU fallback** for memory-constrained environments
7. **Implement proper error handling** for OOM conditions

## Integration with Phase 6

The resource management system is fully integrated with Phase 6 components:

- **Inference System**: Automatic resource allocation and monitoring
- **Audio Preprocessing**: Memory-efficient feature extraction
- **Model Loading**: Smart device selection and memory management
- **Validation Framework**: Resource-aware validation processes
- **Performance Benchmarking**: Memory usage tracking and optimization

This ensures that Phase 6 operates reliably across a wide range of hardware configurations while maximizing performance within available resource constraints.
