#!/usr/bin/env python3
"""
Debug test to isolate the error
"""

import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.preprocessing.tja_processor import TJAProcessor

def debug_single_file():
    """Debug processing of a single file"""
    sample_file = "data/raw/ese/01 Pop/1・2・3 ～Koi ga Hajimaru～/1 2 3 Koi ga Hajimaru.tja"
    
    if not Path(sample_file).exists():
        print(f"Sample file not found: {sample_file}")
        return
    
    try:
        processor = TJAProcessor()
        print("Processing single file...")
        result = processor.process_tja_file_for_training(sample_file)
        
        if result['success']:
            print("✅ Processing successful!")
            print(f"Difficulties: {list(result['notation_data'].keys())}")
        else:
            print(f"❌ Processing failed: {result.get('error', 'Unknown error')}")
            
    except Exception as e:
        print(f"💥 Exception occurred: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_single_file()
