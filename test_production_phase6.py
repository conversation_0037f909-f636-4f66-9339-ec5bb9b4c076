#!/usr/bin/env python3
"""
Production Phase 6 Test Suite

Comprehensive test suite for production Phase 6 system implementing
RFP specifications with RTX 3070 optimization validation.

Test Categories:
- System Integration Tests
- Production Inference Tests
- API Server Tests
- Performance Benchmark Tests
- Resource Management Tests
- Error Handling Tests
"""

import unittest
import sys
import time
import tempfile
import json
import asyncio
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Production Phase 6 imports
from src.phase6.production_inference_system import TJAInferenceSystem, InferenceMetrics
from src.phase6.production_api_server import ProductionAPIServer
from src.phase6.production_benchmark import ProductionBenchmark, PerformanceTarget
from src.phase6.config import create_phase6_config, Phase6Config
from src.utils.resource_manager import ResourceManager
from src.utils.memory_monitor import MemoryMonitor


class TestProductionInferenceSystem(unittest.TestCase):
    """Test production inference system"""
    
    def setUp(self):
        """Setup test configuration"""
        self.config = create_phase6_config(
            experiment_name="test_production_inference",
            debug_mode=True
        )
    
    def test_inference_system_initialization(self):
        """Test inference system initialization"""
        with TJAInferenceSystem(self.config) as inference_system:
            self.assertIsNotNone(inference_system.device)
            self.assertIsNotNone(inference_system.resource_manager)
            self.assertIsNotNone(inference_system.memory_monitor)
            self.assertIsInstance(inference_system.inference_stats, dict)
    
    def test_hardware_detection(self):
        """Test hardware detection and optimization"""
        with TJAInferenceSystem(self.config) as inference_system:
            # Test device setup
            device = inference_system._setup_optimized_device()
            self.assertIn(device.type, ['cuda', 'cpu'])
            
            # Test RTX 3070 detection (if available)
            import torch
            if torch.cuda.is_available():
                gpu_props = torch.cuda.get_device_properties(0)
                if "RTX 3070" in gpu_props.name:
                    self.assertEqual(device.type, 'cuda')
                    # Verify RTX 3070 optimizations are applied
                    self.assertTrue(torch.backends.cudnn.benchmark)
    
    def test_component_initialization(self):
        """Test component initialization"""
        with TJAInferenceSystem(self.config) as inference_system:
            inference_system.initialize_components()
            
            self.assertIsNotNone(inference_system.preprocessor)
            self.assertIsNotNone(inference_system.postprocessor)
            self.assertIsNotNone(inference_system.validator)
    
    def test_input_validation(self):
        """Test input parameter validation"""
        with TJAInferenceSystem(self.config) as inference_system:
            # Test valid inputs
            try:
                inference_system._validate_inputs(
                    "test.ogg", 140.0, 0.0, 9, "oni"
                )
            except FileNotFoundError:
                pass  # Expected for non-existent file
            
            # Test invalid BPM
            with self.assertRaises(ValueError):
                inference_system._validate_inputs(
                    "test.ogg", 500.0, 0.0, 9, "oni"
                )
            
            # Test invalid difficulty
            with self.assertRaises(ValueError):
                inference_system._validate_inputs(
                    "test.ogg", 140.0, 0.0, 15, "oni"
                )
            
            # Test invalid course type
            with self.assertRaises(ValueError):
                inference_system._validate_inputs(
                    "test.ogg", 140.0, 0.0, 9, "invalid"
                )
    
    def test_resource_availability_check(self):
        """Test resource availability checking"""
        with TJAInferenceSystem(self.config) as inference_system:
            # Create temporary file for testing
            with tempfile.NamedTemporaryFile(suffix='.ogg') as temp_file:
                # Should not raise exception for small file
                try:
                    inference_system._check_resource_availability(temp_file.name)
                except MemoryError:
                    self.fail("Resource check failed for small file")
    
    def test_performance_metrics(self):
        """Test performance metrics collection"""
        metrics = InferenceMetrics()
        
        # Test default values
        self.assertEqual(metrics.total_time, 0.0)
        self.assertEqual(metrics.realtime_factor, 0.0)
        self.assertFalse(metrics.success)
        
        # Test metrics calculation
        metrics.total_time = 2.0
        metrics.audio_duration = 60.0
        metrics.realtime_factor = metrics.audio_duration / metrics.total_time
        
        self.assertEqual(metrics.realtime_factor, 30.0)  # 30x realtime
    
    def test_error_handling(self):
        """Test error handling and recovery"""
        with TJAInferenceSystem(self.config) as inference_system:
            # Test error recommendation generation
            memory_error = MemoryError("CUDA out of memory")
            recommendations = inference_system._generate_error_recommendations(memory_error)
            
            self.assertIn("memory", " ".join(recommendations).lower())
            self.assertTrue(len(recommendations) > 0)
            
            # Test file not found error
            file_error = FileNotFoundError("Audio file not found")
            recommendations = inference_system._generate_error_recommendations(file_error)
            
            self.assertIn("file", " ".join(recommendations).lower())


class TestProductionAPIServer(unittest.TestCase):
    """Test production API server"""
    
    def setUp(self):
        """Setup test configuration"""
        self.config = create_phase6_config(
            experiment_name="test_api_server",
            debug_mode=True
        )
    
    def test_api_server_initialization(self):
        """Test API server initialization"""
        server = ProductionAPIServer(self.config)
        
        self.assertIsNotNone(server.app)
        self.assertIsNotNone(server.resource_manager)
        self.assertIsInstance(server.api_stats, dict)
        self.assertEqual(server.api_stats["total_requests"], 0)
    
    def test_rate_limiting(self):
        """Test rate limiting functionality"""
        server = ProductionAPIServer(self.config)
        
        # Test initial rate limit check
        self.assertTrue(server._check_rate_limit())
        
        # Simulate many requests
        for _ in range(server.max_requests_per_minute + 1):
            server._check_rate_limit()
        
        # Should be rate limited now
        self.assertFalse(server._check_rate_limit())
    
    def test_response_time_stats(self):
        """Test response time statistics"""
        server = ProductionAPIServer(self.config)
        
        # Test initial stats
        self.assertEqual(server.api_stats["average_response_time"], 0.0)
        
        # Update with response times
        server.api_stats["successful_requests"] = 1
        server._update_response_time_stats(1.5)
        
        self.assertEqual(server.api_stats["average_response_time"], 1.5)


class TestProductionBenchmark(unittest.TestCase):
    """Test production benchmark system"""
    
    def setUp(self):
        """Setup test configuration"""
        self.config = create_phase6_config(
            experiment_name="test_benchmark",
            enable_benchmarking=True,
            debug_mode=True
        )
    
    def test_performance_targets(self):
        """Test performance targets for RTX 3070"""
        targets = PerformanceTarget()
        
        # Test default targets
        self.assertEqual(targets.realtime_factor, 10.0)
        self.assertEqual(targets.max_memory_usage_gb, 2.0)
        self.assertEqual(targets.max_gpu_memory_gb, 1.5)
        self.assertEqual(targets.min_quality_score, 0.8)
    
    def test_hardware_detection(self):
        """Test hardware detection in benchmark"""
        # Mock inference system
        mock_inference_system = Mock()
        
        benchmark = ProductionBenchmark(self.config.benchmark, mock_inference_system)
        
        self.assertIsInstance(benchmark.hardware_info, dict)
        self.assertIn("cpu_cores", benchmark.hardware_info)
        self.assertIn("system_memory_gb", benchmark.hardware_info)
        self.assertIn("gpu_available", benchmark.hardware_info)
    
    def test_synthetic_test_cases(self):
        """Test synthetic test case generation"""
        mock_inference_system = Mock()
        benchmark = ProductionBenchmark(self.config.benchmark, mock_inference_system)
        
        synthetic_cases = benchmark._create_synthetic_test_cases()
        
        self.assertTrue(len(synthetic_cases) > 0)
        
        for case in synthetic_cases:
            self.assertIn("bpm", case)
            self.assertIn("difficulty", case)
            self.assertIn("expected_duration", case)
            self.assertTrue(60 <= case["bpm"] <= 300)
            self.assertIn(case["difficulty"], [8, 9, 10])
    
    def test_benchmark_result_structure(self):
        """Test benchmark result structure"""
        from src.phase6.production_benchmark import BenchmarkResult
        
        result = BenchmarkResult(
            category="test",
            test_name="test_case",
            score=0.85,
            passed=True,
            metrics={"time": 1.5},
            details={"info": "test"}
        )
        
        self.assertEqual(result.category, "test")
        self.assertEqual(result.score, 0.85)
        self.assertTrue(result.passed)
        self.assertIsInstance(result.timestamp, float)


class TestResourceManagement(unittest.TestCase):
    """Test resource management integration"""
    
    def test_resource_manager_integration(self):
        """Test resource manager integration"""
        config = create_phase6_config(debug_mode=True)
        
        with TJAInferenceSystem(config) as inference_system:
            # Test resource manager is initialized
            self.assertIsNotNone(inference_system.resource_manager)
            
            # Test resource allocation
            success, message = inference_system.resource_manager.allocate_resources(
                "test_component", memory_gb=0.1, gpu_memory_gb=0.05
            )
            
            self.assertTrue(success)
            
            # Test deallocation
            inference_system.resource_manager.deallocate_resources("test_component")
    
    def test_memory_monitoring(self):
        """Test memory monitoring"""
        memory_monitor = MemoryMonitor()
        
        # Test memory stats
        stats = memory_monitor.get_memory_stats()
        
        self.assertGreaterEqual(stats.system_memory_gb, 0)
        self.assertGreaterEqual(stats.system_memory_percent, 0)
        self.assertLessEqual(stats.system_memory_percent, 100)
    
    def test_memory_cleanup(self):
        """Test memory cleanup functionality"""
        config = create_phase6_config(debug_mode=True)
        
        with TJAInferenceSystem(config) as inference_system:
            # Get initial memory
            initial_stats = inference_system.memory_monitor.get_memory_stats()
            
            # Trigger cleanup
            inference_system.resource_manager.cleanup_memory()
            
            # Memory cleanup should not raise exceptions
            self.assertTrue(True)  # If we get here, cleanup worked


class TestSystemIntegration(unittest.TestCase):
    """Test complete system integration"""
    
    def test_end_to_end_configuration(self):
        """Test end-to-end configuration"""
        config = create_phase6_config(
            experiment_name="integration_test",
            enable_validation=True,
            enable_benchmarking=True,
            debug_mode=True
        )
        
        # Test configuration completeness
        self.assertIsNotNone(config.resource)
        self.assertIsNotNone(config.inference)
        self.assertIsNotNone(config.validation)
        self.assertIsNotNone(config.benchmark)
        
        # Test resource limits are set
        self.assertGreater(config.resource.max_system_memory_gb, 0)
        self.assertGreaterEqual(config.resource.max_gpu_memory_gb, 0)
    
    def test_production_readiness(self):
        """Test production readiness indicators"""
        config = create_phase6_config(debug_mode=False)  # Production mode
        
        # Test production settings
        self.assertFalse(config.debug_mode)
        self.assertEqual(config.log_level, "INFO")
        
        # Test inference settings are production-ready
        self.assertTrue(config.inference.enable_memory_optimization)
        self.assertTrue(config.inference.clear_cache_between_inferences)
    
    def test_rtx_3070_optimization_detection(self):
        """Test RTX 3070 optimization detection"""
        import torch
        
        if torch.cuda.is_available():
            gpu_props = torch.cuda.get_device_properties(0)
            
            if "RTX 3070" in gpu_props.name:
                config = create_phase6_config()
                
                with TJAInferenceSystem(config) as inference_system:
                    # RTX 3070 should be detected and optimized
                    self.assertEqual(inference_system.device.type, 'cuda')
                    
                    # Check if CUDA optimizations are enabled
                    self.assertTrue(torch.backends.cudnn.benchmark)
    
    def test_error_recovery_integration(self):
        """Test error recovery integration"""
        config = create_phase6_config(debug_mode=True)
        
        with TJAInferenceSystem(config) as inference_system:
            # Test that error recovery settings are configured
            self.assertGreater(config.resource.oom_retry_attempts, 0)
            self.assertGreater(config.resource.oom_retry_delay_seconds, 0)
            
            # Test error recommendation system
            test_error = ValueError("Test error")
            recommendations = inference_system._generate_error_recommendations(test_error)
            
            self.assertIsInstance(recommendations, list)
            self.assertGreater(len(recommendations), 0)


def run_production_tests():
    """Run all production tests"""
    print("🧪 Production Phase 6 Test Suite")
    print("=" * 50)
    
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test classes
    test_classes = [
        TestProductionInferenceSystem,
        TestProductionAPIServer,
        TestProductionBenchmark,
        TestResourceManagement,
        TestSystemIntegration
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Print summary
    print(f"\n📊 Production Test Results:")
    print(f"   Tests run: {result.testsRun}")
    print(f"   Failures: {len(result.failures)}")
    print(f"   Errors: {len(result.errors)}")
    print(f"   Skipped: {len(result.skipped)}")
    
    success_rate = (result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100
    print(f"   Success rate: {success_rate:.1f}%")
    
    # Production readiness assessment
    if success_rate >= 95.0:
        print(f"\n🎉 PRODUCTION READY!")
        print(f"   ✅ All critical systems validated")
        print(f"   ✅ RTX 3070 optimizations verified")
        print(f"   ✅ Error handling comprehensive")
        print(f"   ✅ Resource management robust")
    elif success_rate >= 85.0:
        print(f"\n⚠️  PRODUCTION READY WITH MONITORING")
        print(f"   ✅ Core functionality validated")
        print(f"   ⚠️  Some edge cases need attention")
    else:
        print(f"\n❌ NOT PRODUCTION READY")
        print(f"   ❌ Critical issues need resolution")
    
    if result.failures:
        print(f"\n❌ Failures:")
        for test, traceback in result.failures:
            print(f"   {test}: {traceback.split('AssertionError:')[-1].strip()}")
    
    if result.errors:
        print(f"\n⚠️  Errors:")
        for test, traceback in result.errors:
            print(f"   {test}: {traceback.split('Exception:')[-1].strip()}")
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_production_tests()
    sys.exit(0 if success else 1)
