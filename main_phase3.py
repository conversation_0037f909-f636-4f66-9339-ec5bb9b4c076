#!/usr/bin/env python3
"""
TJA Generator - Phase 3: Model Training and Generation
Main entry point for Phase 3 deep learning pipeline

Hardware-optimized for RTX 3070 system with 8GB VRAM.
Trains transformer-based model for TJA generation from audio features.
"""

import sys
import time
import argparse
import logging
import json
import torch
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.model.tja_generator import TJ<PERSON>eneratorModel
from src.model import PHASE_3_MODEL_CONFIG
from src.training.data_loader import TJADataLoader
from src.training.trainer import TJATrainer
from src.training import PHASE_3_TRAINING_CONFIG
from src.utils.hardware_monitor import get_system_info
from src.utils.memory_monitor import setup_memory_optimized_environment


def setup_logging(log_level: str = "INFO") -> logging.Logger:
    """Setup logging configuration"""
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    # Configure root logger
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_dir / "phase3_main.log"),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    return logging.getLogger(__name__)


def validate_phase2_outputs():
    """Validate Phase 2 outputs are available"""
    print("=" * 60)
    print("PHASE 3: PHASE 2 OUTPUT VALIDATION")
    print("=" * 60)
    
    # Check for Phase 3 input catalog
    catalog_path = Path("data/processed/audio_features/phase3_input_catalog.json")
    if not catalog_path.exists():
        print(f"❌ Phase 3 input catalog not found: {catalog_path}")
        return False
    
    print(f"✅ Phase 3 input catalog found: {catalog_path}")
    
    # Check catalog content
    try:
        with open(catalog_path, 'r', encoding='utf-8') as f:
            catalog = json.load(f)
        
        songs = catalog.get("songs", [])
        phase3_ready = sum(1 for song in songs if song.get("phase3_ready", False))
        
        print(f"📊 Total songs in catalog: {len(songs)}")
        print(f"🎯 Phase 3 ready songs: {phase3_ready}")
        print(f"📈 Feature extraction success rate: {catalog.get('dataset_statistics', {}).get('feature_extraction_success_rate', 0):.1%}")
        
        if phase3_ready == 0:
            print("❌ No songs ready for Phase 3 processing")
            return False
        
        # Check feature tensor files exist
        feature_dir = Path("data/processed/audio_features/feature_tensors")
        if not feature_dir.exists():
            print(f"❌ Feature tensor directory not found: {feature_dir}")
            return False
        
        tensor_files = list(feature_dir.glob("*.pt"))
        print(f"📁 Feature tensor files: {len(tensor_files)}")
        
        if len(tensor_files) == 0:
            print("❌ No feature tensor files found")
            return False
        
        print("✅ Phase 2 outputs validated successfully")
        return True
        
    except Exception as e:
        print(f"❌ Error validating Phase 2 outputs: {e}")
        return False


def validate_hardware_environment():
    """Validate hardware environment for Phase 3"""
    print("\nHARDWARE ENVIRONMENT VALIDATION")
    print("=" * 60)
    
    # Get system information
    system_info = get_system_info()
    
    print("Hardware Configuration:")
    print(f"  CPU Cores: {system_info['cpu']['physical_cores']} physical, {system_info['cpu']['logical_cores']} logical")
    print(f"  Memory: {system_info['memory']['total_gb']:.1f}GB total, {system_info['memory']['available_gb']:.1f}GB available")
    print(f"  GPU: {'Available' if system_info['gpu']['cuda_available'] else 'Not Available'}")
    
    if system_info['gpu']['cuda_available']:
        print(f"    Name: {system_info['gpu']['name']}")
        print(f"    Memory: {system_info['gpu']['memory_total_gb']:.1f}GB")
        if 'compute_capability' in system_info['gpu']:
            print(f"    Compute Capability: {system_info['gpu']['compute_capability']}")
        else:
            print(f"    Compute Capability: Available")
    
    # Validate requirements for Phase 3
    warnings = []
    
    if system_info['cpu']['logical_cores'] < 8:
        warnings.append(f"Recommended 8+ logical cores for Phase 3, found {system_info['cpu']['logical_cores']}")
    
    if system_info['memory']['total_gb'] < 16:
        warnings.append(f"Recommended 16GB+ RAM for Phase 3, found {system_info['memory']['total_gb']:.1f}GB")
    
    if not system_info['gpu']['cuda_available']:
        warnings.append("CUDA not available - Phase 3 requires GPU acceleration")
        return False
    elif system_info['gpu']['memory_total_gb'] < 6:
        warnings.append(f"Recommended 8GB+ VRAM for Phase 3, found {system_info['gpu']['memory_total_gb']:.1f}GB")
    
    if warnings:
        print("\nWarnings:")
        for warning in warnings:
            print(f"  ⚠️  {warning}")
    else:
        print("\n✅ Hardware configuration optimal for Phase 3")
    
    print("=" * 60)
    return True


def create_model_and_trainer(config: dict, output_dir: str):
    """Create model and trainer instances"""
    print("\n🤖 INITIALIZING MODEL AND TRAINER")
    print("=" * 60)

    # Setup memory-optimized environment
    print("Setting up memory-optimized environment...")
    memory_monitor = setup_memory_optimized_environment()
    memory_monitor.log_memory_status("Before model creation")

    # Create model
    print("Creating TJA Generator model...")
    model = TJAGeneratorModel(config["model"])
    
    # Log model information
    model_info = model.get_model_size()
    print(f"📊 Model Statistics:")
    print(f"  Total parameters: {model_info['total_parameters']:,}")
    print(f"  Trainable parameters: {model_info['trainable_parameters']:,}")
    print(f"  Model size: {model_info['model_size_mb']:.1f} MB")
    
    print(f"🏗️  Component sizes:")
    for component, size in model_info['component_sizes'].items():
        print(f"    {component}: {size:,} parameters")
    
    # Log memory after model creation
    memory_monitor.log_memory_status("After model creation")

    # Create trainer
    print("\nCreating trainer...")
    trainer = TJATrainer(model, config["training"], output_dir)

    # Final memory check
    memory_monitor.log_memory_status("After trainer creation")

    print("✅ Model and trainer initialized successfully")
    return model, trainer


def run_training(trainer: TJATrainer, config: dict):
    """Run the training pipeline"""
    print("\n🚀 STARTING TRAINING PIPELINE")
    print("=" * 60)
    
    # Create data loaders
    print("Creating data loaders...")
    data_loader = TJADataLoader(config["training"])
    
    train_loader, val_loader, test_loader = data_loader.create_data_loaders(
        catalog_path="data/processed/audio_features/phase3_input_catalog.json",
        phase1_catalog_path="data/processed/catalog.json"
    )
    
    print(f"📊 Data splits:")
    print(f"  Training batches: {len(train_loader)}")
    print(f"  Validation batches: {len(val_loader)}")
    print(f"  Test batches: {len(test_loader)}")
    
    # Start training
    print("\n🎯 Starting model training...")
    start_time = time.time()
    
    try:
        results = trainer.train(train_loader, val_loader, test_loader)
        training_time = time.time() - start_time
        
        print("\n" + "=" * 60)
        print("TRAINING COMPLETED SUCCESSFULLY!")
        print("=" * 60)
        print(f"⏱️  Total training time: {training_time:.1f} seconds ({training_time/3600:.2f} hours)")
        print(f"📈 Final validation loss: {results.get('best_val_loss', 'N/A'):.4f}")
        print(f"🎯 Total epochs: {results.get('total_epochs', 'N/A')}")
        print(f"📊 Total steps: {results.get('total_steps', 'N/A')}")
        
        if "test_metrics" in results:
            test_metrics = results["test_metrics"]
            print(f"\n🧪 Test Results:")
            print(f"  Test Loss: {test_metrics.get('loss', 'N/A'):.4f}")
            print(f"  Test Accuracy: {test_metrics.get('note_accuracy', 'N/A'):.3f}")
        
        if "generation_evaluation" in results:
            gen_eval = results["generation_evaluation"]
            agg_metrics = gen_eval.get("aggregate_metrics", {})
            print(f"\n🎵 Generation Quality:")
            if "token_accuracy" in agg_metrics:
                print(f"  Token Accuracy: {agg_metrics['token_accuracy']['mean']:.3f}")
            if "difficulty_appropriateness" in agg_metrics:
                print(f"  Difficulty Appropriateness: {agg_metrics['difficulty_appropriateness']['mean']:.3f}")
            if "pattern_coherence" in agg_metrics:
                print(f"  Pattern Coherence: {agg_metrics['pattern_coherence']['mean']:.3f}")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Training failed: {e}")
        return False


def run_inference_demo(model: TJAGeneratorModel, config: dict):
    """Run inference demonstration"""
    print("\n🎼 INFERENCE DEMONSTRATION")
    print("=" * 60)
    
    try:
        # Load a sample for demonstration
        data_loader = TJADataLoader(config["training"])
        _, _, test_loader = data_loader.create_data_loaders(
            catalog_path="data/processed/audio_features/phase3_input_catalog.json",
            phase1_catalog_path="data/processed/catalog.json"
        )
        
        # Get first batch
        sample_batch = next(iter(test_loader))
        
        # Move to GPU
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        audio_features = sample_batch["audio_features"][:1].to(device)  # First sample only
        
        print(f"🎵 Generating TJA for sample audio...")
        print(f"  Audio features shape: {audio_features.shape}")
        
        # Generate for different difficulties
        for difficulty in [0, 1, 2]:  # Oni 8, 9, 10
            print(f"\n🎯 Generating Oni {8 + difficulty} chart...")
            
            generated = model.generate(
                audio_features=audio_features,
                difficulty=difficulty,
                temperature=1.0,
                top_k=50,
                top_p=0.9
            )
            
            if "generated_sequence" in generated:
                sequence = generated["generated_sequence"][0].cpu().numpy()
                
                # Analyze generated sequence
                non_blank_ratio = (sequence != 0).mean()
                unique_notes = len(set(sequence))
                
                print(f"    Generated sequence length: {len(sequence)}")
                print(f"    Note density: {non_blank_ratio:.2%}")
                print(f"    Unique note types: {unique_notes}")
                
                # Show first 20 notes
                preview = sequence[:20]
                note_names = ["_", "D", "K", "D+", "K+", "R", "E", "S"]
                preview_str = "".join(note_names[min(note, 7)] for note in preview)
                print(f"    Preview: {preview_str}")
        
        print("\n✅ Inference demonstration completed successfully")
        return True
        
    except Exception as e:
        print(f"\n❌ Inference demonstration failed: {e}")
        return False


def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(
        description="TJA Generator Phase 3: Model Training and Generation",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main_phase3.py                          # Full training pipeline
  python main_phase3.py --validate-only          # Validation only
  python main_phase3.py --inference-only         # Inference demo only
  python main_phase3.py --config custom.json     # Custom configuration
  python main_phase3.py --output-dir custom/     # Custom output directory
        """
    )
    
    parser.add_argument(
        '--config',
        default=None,
        help='Path to custom configuration file (JSON)'
    )
    
    parser.add_argument(
        '--output-dir',
        default='outputs/phase3_training',
        help='Output directory for training results'
    )
    
    parser.add_argument(
        '--validate-only',
        action='store_true',
        help='Only validate Phase 2 outputs and hardware, do not train'
    )
    
    parser.add_argument(
        '--inference-only',
        action='store_true',
        help='Only run inference demonstration (requires trained model)'
    )
    
    parser.add_argument(
        '--resume-from',
        default=None,
        help='Path to checkpoint to resume training from'
    )
    
    parser.add_argument(
        '--log-level',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        default='INFO',
        help='Logging level (default: INFO)'
    )
    
    args = parser.parse_args()
    
    # Setup logging
    logger = setup_logging(args.log_level)
    
    try:
        print("🎯 TJA Generator - Phase 3: Model Training and Generation")
        print("🔧 Hardware-optimized for RTX 3070 with 8GB VRAM")
        print("📅 " + time.strftime("%Y-%m-%d %H:%M:%S"))
        print()
        
        # Load configuration
        if args.config:
            with open(args.config, 'r') as f:
                custom_config = json.load(f)
            # Merge with default config
            config = {
                "model": PHASE_3_MODEL_CONFIG["architecture"],
                "training": {**PHASE_3_TRAINING_CONFIG, **custom_config.get("training", {})}
            }
        else:
            config = {
                "model": PHASE_3_MODEL_CONFIG["architecture"],
                "training": PHASE_3_TRAINING_CONFIG
            }
        
        # Validate Phase 2 outputs
        phase2_ok = validate_phase2_outputs()
        if not phase2_ok:
            print("❌ Phase 2 validation failed. Please run Phase 2 first.")
            return 1
        
        # Validate hardware environment
        hardware_ok = validate_hardware_environment()
        if not hardware_ok:
            print("❌ Hardware validation failed. RTX 3070 or equivalent required.")
            return 1
        
        if args.validate_only:
            print("✅ Validation complete. Exiting (--validate-only specified).")
            return 0
        
        # Create model and trainer
        model, trainer = create_model_and_trainer(config, args.output_dir)
        
        if args.inference_only:
            # Load trained model if available
            best_model_path = Path(args.output_dir) / "best_model.pt"
            if best_model_path.exists():
                print(f"📂 Loading trained model from {best_model_path}")
                model, _ = TJAGeneratorModel.load_checkpoint(str(best_model_path))
                success = run_inference_demo(model, config)
            else:
                print("❌ No trained model found. Please train first or specify --resume-from.")
                return 1
        else:
            # Run training
            success = run_training(trainer, config)
            
            if success:
                # Run inference demo with trained model
                print("\n🎼 Running inference demonstration...")
                run_inference_demo(model, config)
        
        if success:
            print("\n🎉 Phase 3 completed successfully!")
            print("📋 Ready for TJA file generation and deployment")
            return 0
        else:
            print("\n❌ Phase 3 failed!")
            return 1
            
    except KeyboardInterrupt:
        print("\n⏹️  Processing interrupted by user")
        return 1
    except Exception as e:
        logger.error(f"Unexpected error: {e}", exc_info=True)
        print(f"\n💥 Unexpected error: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
