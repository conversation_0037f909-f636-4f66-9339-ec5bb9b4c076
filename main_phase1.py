#!/usr/bin/env python3
"""
TJA Generator - Phase 1: Data Analysis and Preprocessing
Main entry point for Phase 1 processing pipeline

Hardware-optimized for RTX 3070 system with 32GB RAM.
Processes ~2,800 TJA files with strict metadata/notation separation.
"""

import sys
import time
import argparse
import logging
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.preprocessing.data_analyzer import T<PERSON><PERSON>ata<PERSON>nalyzer
from src.utils.hardware_monitor import get_system_info, setup_hardware_optimized_processing
from src.utils.encoding_detector import validate_encoding_detection
from src.paths.path_manager import PathManager, PathType


def setup_logging(log_level: str = "INFO") -> logging.Logger:
    """Setup logging configuration"""
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    # Configure root logger
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_dir / "phase1_main.log"),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    return logging.getLogger(__name__)


def validate_environment():
    """Validate hardware and software environment"""
    print("=" * 60)
    print("PHASE 1: ENVIRONMENT VALIDATION")
    print("=" * 60)
    
    # Get system information
    system_info = get_system_info()
    
    print("Hardware Configuration:")
    print(f"  CPU Cores: {system_info['cpu']['physical_cores']} physical, {system_info['cpu']['logical_cores']} logical")
    print(f"  Memory: {system_info['memory']['total_gb']:.1f}GB total, {system_info['memory']['available_gb']:.1f}GB available")
    print(f"  GPU: {'Available' if system_info['gpu']['cuda_available'] else 'Not Available'}")
    
    if system_info['gpu']['cuda_available']:
        print(f"    Name: {system_info['gpu']['name']}")
        print(f"    Memory: {system_info['gpu']['memory_total_gb']:.1f}GB")
    
    # Validate requirements
    warnings = []
    
    if system_info['cpu']['logical_cores'] < 12:
        warnings.append(f"Recommended 16+ logical cores, found {system_info['cpu']['logical_cores']}")
    
    if system_info['memory']['total_gb'] < 30:
        warnings.append(f"Recommended 32GB RAM, found {system_info['memory']['total_gb']:.1f}GB")
    
    if not system_info['gpu']['cuda_available']:
        warnings.append("CUDA not available - GPU acceleration disabled")
    
    if warnings:
        print("\nWarnings:")
        for warning in warnings:
            print(f"  ⚠️  {warning}")
    else:
        print("\n✅ Hardware configuration optimal")
    
    print("=" * 60)
    return len(warnings) == 0


def discover_and_validate_data(data_dir: str):
    """Discover and validate TJA data"""
    print("DATA DISCOVERY AND VALIDATION")
    print("=" * 60)
    
    data_path = Path(data_dir)
    if not data_path.exists():
        print(f"❌ Data directory not found: {data_dir}")
        return []
    
    # Discover TJA files
    tja_files = list(data_path.rglob("*.tja"))
    print(f"📁 Found {len(tja_files)} TJA files in {data_dir}")
    
    if len(tja_files) == 0:
        print("❌ No TJA files found")
        return []
    
    # Sample validation
    print("\n🔍 Validating sample files...")
    sample_files = tja_files[:5]  # Validate first 5 files
    
    for i, tja_file in enumerate(sample_files, 1):
        try:
            # Test encoding detection
            from src.utils.encoding_detector import detect_file_encoding
            encoding = detect_file_encoding(str(tja_file))
            
            # Test basic parsing
            from src.parsing.custom_tja_parser import CustomTJAParser
            parser = CustomTJAParser(str(tja_file))
            
            print(f"  {i}. {tja_file.name} - ✅ (encoding: {encoding})")
            
        except Exception as e:
            print(f"  {i}. {tja_file.name} - ❌ Error: {str(e)}")
    
    print(f"\n✅ Data validation complete - {len(tja_files)} files ready for processing")
    print("=" * 60)
    return tja_files


def run_phase1_processing(data_dir: str, test_mode: bool = False, test_count: int = 50):
    """Run the main Phase 1 processing pipeline"""
    print("PHASE 1: DATA ANALYSIS AND PREPROCESSING")
    print("=" * 60)
    
    start_time = time.time()
    
    # Initialize TJA data analyzer with hardware optimization
    config = setup_hardware_optimized_processing()
    analyzer = TjaDataAnalyzer(config)

    print(f"🚀 Initialized TjaDataAnalyzer with {config['parallel_workers']} workers")
    print(f"💾 Memory allocation: {config['memory_per_worker_gb']}GB per worker")
    print(f"📦 Batch size: {config['batch_size']} files per batch")
    
    if test_mode:
        print(f"🧪 Test mode: Processing {test_count} files only")
    
    # Discover TJA files
    tja_files = analyzer.discover_tja_files(data_dir)
    
    if not tja_files:
        print("❌ No TJA files found for processing")
        return False
    
    print(f"📊 Processing {len(tja_files)} TJA files...")
    
    # Process files
    results = analyzer.process_tja_files(tja_files, test_mode=test_mode, test_count=test_count)
    
    # Display results
    processing_time = time.time() - start_time
    stats = results['statistics']
    
    print("\n" + "=" * 60)
    print("PROCESSING RESULTS")
    print("=" * 60)
    print(f"📈 Total files: {stats['total_files']}")
    print(f"✅ Successful: {stats['successful_files']} ({stats['success_rate']:.1%})")
    print(f"❌ Failed: {stats['failed_files']}")
    print(f"⏱️  Processing time: {processing_time:.1f}s")
    print(f"🚄 Speed: {stats['files_per_second']:.1f} files/second")
    print(f"🎵 WAVE field compliance: {stats['wave_field_compliance']:.1%}")
    print(f"🎯 Phase 2 eligible: {results['catalog']['processing_statistics']['phase_2_eligible']}")
    
    # Audio pairing statistics
    print(f"\n📻 Audio Pairing:")
    print(f"  WAVE field resolved: {stats['wave_field_resolved']}")
    print(f"  Filename fallback: {stats['filename_fallback_used']}")
    print(f"  Missing audio: {stats['missing_audio']}")
    print(f"  Empty WAVE field: {stats['wave_field_empty']}")
    
    # Training data statistics
    training_stats = results['catalog']['training_statistics']
    print(f"\n🤖 Training Data:")
    print(f"  Total note sequences: {training_stats['total_notation_sequences']:,}")
    print(f"  Valid sequences: {training_stats['valid_notation_sequences']:,}")
    print(f"  Total notes: {training_stats['total_notes']:,}")
    
    # Validation summary
    validation = results['catalog']['validation_summary']
    print(f"\n✅ Validation Summary:")
    print(f"  Overall success rate: {validation['overall_success_rate']:.1%}")
    print(f"  Notation purity score: {validation['notation_purity_score']:.1%}")
    print(f"  Training readiness: {validation['training_readiness']:.1%}")
    
    print("=" * 60)
    print("✅ Phase 1 processing complete!")
    print(f"📄 Catalog saved to: data/processed/catalog.json")
    print(f"📊 Statistics saved to: data/processed/processing_statistics.json")
    
    if results['errors']:
        print(f"⚠️  {len(results['errors'])} errors logged to: data/processed/processing_errors.json")
    
    return True


def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(
        description="TJA Generator Phase 1: Data Analysis and Preprocessing",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main_phase1.py                          # Process all files
  python main_phase1.py --test                   # Test mode (50 files)
  python main_phase1.py --test --count 100       # Test mode (100 files)
  python main_phase1.py --data-dir custom/path   # Custom data directory
  python main_phase1.py --validate-only          # Validation only
        """
    )
    
    parser.add_argument(
        '--data-dir',
        default=None,
        help='Directory containing TJA files (default: standardized data/raw path)'
    )
    
    parser.add_argument(
        '--test', 
        action='store_true',
        help='Run in test mode (process subset of files)'
    )
    
    parser.add_argument(
        '--count', 
        type=int, 
        default=50,
        help='Number of files to process in test mode (default: 50)'
    )
    
    parser.add_argument(
        '--validate-only', 
        action='store_true',
        help='Only validate environment and data, do not process'
    )
    
    parser.add_argument(
        '--log-level', 
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        default='INFO',
        help='Logging level (default: INFO)'
    )
    
    args = parser.parse_args()
    
    # Setup logging
    logger = setup_logging(args.log_level)
    
    try:
        print("🎯 TJA Generator - Phase 1: Data Analysis and Preprocessing")
        print("🔧 Hardware-optimized for RTX 3070 with 32GB RAM")
        print("📅 " + time.strftime("%Y-%m-%d %H:%M:%S"))
        print()
        
        # Validate environment
        env_ok = validate_environment()
        if not env_ok:
            print("⚠️  Environment validation warnings detected")
            print("   Processing will continue but performance may be suboptimal")
            print()
        
        # Initialize path manager for standardized paths
        path_manager = PathManager()

        # Use standardized data directory if not specified
        if args.data_dir is None:
            data_directory = path_manager.get_standardized_path(PathType.DATA_RAW, "ese")
        else:
            data_directory = path_manager.resolve_path(args.data_dir)

        # Discover and validate data
        tja_files = discover_and_validate_data(str(data_directory))
        if not tja_files:
            print("❌ No valid TJA files found. Exiting.")
            return 1
        
        if args.validate_only:
            print("✅ Validation complete. Exiting (--validate-only specified).")
            return 0
        
        # Run processing with standardized path
        success = run_phase1_processing(
            str(data_directory),
            test_mode=args.test,
            test_count=args.count
        )
        
        if success:
            print("\n🎉 Phase 1 completed successfully!")
            print("📋 Ready for Phase 2: Pattern Analysis and Feature Extraction")
            return 0
        else:
            print("\n❌ Phase 1 failed!")
            return 1
            
    except KeyboardInterrupt:
        print("\n⏹️  Processing interrupted by user")
        return 1
    except Exception as e:
        logger.error(f"Unexpected error: {e}", exc_info=True)
        print(f"\n💥 Unexpected error: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
