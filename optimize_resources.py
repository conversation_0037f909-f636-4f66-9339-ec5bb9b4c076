#!/usr/bin/env python3
"""
Resource Optimization Utility for Phase 6 TJA Generator

Provides detailed system resource analysis, optimization recommendations,
and dynamic resource allocation for RTX 3070 systems.
"""

import sys
import psutil
import torch
import time
import json
import logging
from pathlib import Path
from typing import Dict, Any, <PERSON><PERSON>, Optional
from dataclasses import dataclass, asdict

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.phase6.config import create_phase6_config, Phase6Config
from src.utils.resource_manager import ResourceManager, ResourceLimits
from src.utils.memory_monitor import MemoryMonitor


@dataclass
class SystemAnalysis:
    """Comprehensive system resource analysis"""
    # Hardware specs
    total_ram_gb: float
    available_ram_gb: float
    cpu_cores: int
    cpu_frequency_mhz: float
    gpu_name: str
    gpu_memory_gb: float
    gpu_compute_capability: str
    
    # Current allocation
    current_ram_limit_gb: float
    current_gpu_limit_gb: float
    current_ram_utilization: float
    current_gpu_utilization: float
    
    # Optimization recommendations
    recommended_ram_gb: float
    recommended_gpu_gb: float
    optimization_potential: float
    performance_gain_estimate: float


class ResourceOptimizer:
    """Advanced resource optimization for Phase 6 TJA Generator"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
    def analyze_system(self) -> SystemAnalysis:
        """Perform comprehensive system analysis"""
        # System memory analysis
        memory = psutil.virtual_memory()
        total_ram_gb = memory.total / (1024**3)
        available_ram_gb = memory.available / (1024**3)
        
        # CPU analysis
        cpu_cores = psutil.cpu_count()
        cpu_freq = psutil.cpu_freq()
        cpu_frequency_mhz = cpu_freq.current if cpu_freq else 0
        
        # GPU analysis
        gpu_name = "No GPU"
        gpu_memory_gb = 0.0
        gpu_compute_capability = "N/A"
        
        if torch.cuda.is_available():
            gpu_props = torch.cuda.get_device_properties(0)
            gpu_name = gpu_props.name
            gpu_memory_gb = gpu_props.total_memory / (1024**3)
            gpu_compute_capability = f"{gpu_props.major}.{gpu_props.minor}"
        
        # Current configuration analysis
        config = create_phase6_config(debug_mode=True)
        current_ram_limit_gb = config.resource.max_system_memory_gb or 0.0
        current_gpu_limit_gb = config.resource.max_gpu_memory_gb or 0.0
        
        # Calculate utilization ratios
        current_ram_utilization = current_ram_limit_gb / available_ram_gb if available_ram_gb > 0 else 0.0
        current_gpu_utilization = current_gpu_limit_gb / gpu_memory_gb if gpu_memory_gb > 0 else 0.0
        
        # Generate optimization recommendations
        recommended_ram_gb, recommended_gpu_gb = self._calculate_optimal_allocation(
            available_ram_gb, gpu_memory_gb, gpu_name
        )
        
        # Calculate optimization potential
        optimization_potential = self._calculate_optimization_potential(
            current_ram_limit_gb, current_gpu_limit_gb,
            recommended_ram_gb, recommended_gpu_gb
        )
        
        # Estimate performance gain
        performance_gain_estimate = self._estimate_performance_gain(
            current_ram_utilization, current_gpu_utilization,
            recommended_ram_gb / available_ram_gb if available_ram_gb > 0 else 0,
            recommended_gpu_gb / gpu_memory_gb if gpu_memory_gb > 0 else 0
        )
        
        return SystemAnalysis(
            total_ram_gb=total_ram_gb,
            available_ram_gb=available_ram_gb,
            cpu_cores=cpu_cores,
            cpu_frequency_mhz=cpu_frequency_mhz,
            gpu_name=gpu_name,
            gpu_memory_gb=gpu_memory_gb,
            gpu_compute_capability=gpu_compute_capability,
            current_ram_limit_gb=current_ram_limit_gb,
            current_gpu_limit_gb=current_gpu_limit_gb,
            current_ram_utilization=current_ram_utilization,
            current_gpu_utilization=current_gpu_utilization,
            recommended_ram_gb=recommended_ram_gb,
            recommended_gpu_gb=recommended_gpu_gb,
            optimization_potential=optimization_potential,
            performance_gain_estimate=performance_gain_estimate
        )
    
    def _calculate_optimal_allocation(self, available_ram_gb: float, 
                                    gpu_memory_gb: float, gpu_name: str) -> Tuple[float, float]:
        """Calculate optimal resource allocation"""
        # RAM allocation: 75% of available (user preference: 70-80%)
        optimal_ram_gb = min(available_ram_gb * 0.75, 25.0)  # Cap at 25GB for stability
        
        # GPU allocation: RTX 3070 optimized
        if "RTX 3070" in gpu_name:
            optimal_gpu_gb = 6.8  # 85% of 8GB for RTX 3070
        elif gpu_memory_gb > 0:
            optimal_gpu_gb = min(gpu_memory_gb * 0.80, 6.8)  # 80% for other GPUs
        else:
            optimal_gpu_gb = 0.0
        
        return optimal_ram_gb, optimal_gpu_gb
    
    def _calculate_optimization_potential(self, current_ram: float, current_gpu: float,
                                        recommended_ram: float, recommended_gpu: float) -> float:
        """Calculate optimization potential as percentage improvement"""
        if current_ram == 0 and current_gpu == 0:
            return 0.0
        
        current_total = current_ram + current_gpu
        recommended_total = recommended_ram + recommended_gpu
        
        if current_total == 0:
            return 100.0
        
        improvement = ((recommended_total - current_total) / current_total) * 100
        return max(0.0, improvement)
    
    def _estimate_performance_gain(self, current_ram_util: float, current_gpu_util: float,
                                 optimal_ram_util: float, optimal_gpu_util: float) -> float:
        """Estimate performance gain percentage"""
        # Simple heuristic based on resource utilization improvement
        ram_gain = max(0, optimal_ram_util - current_ram_util) * 50  # RAM contributes 50% to performance
        gpu_gain = max(0, optimal_gpu_util - current_gpu_util) * 70  # GPU contributes 70% to performance
        
        total_gain = min(ram_gain + gpu_gain, 100.0)  # Cap at 100%
        return total_gain
    
    def print_analysis(self, analysis: SystemAnalysis):
        """Print detailed system analysis"""
        print("🔍 Advanced System Resource Analysis")
        print("=" * 50)
        
        # Hardware specifications
        print(f"\n💻 Hardware Specifications:")
        print(f"   RAM: {analysis.total_ram_gb:.1f}GB total, {analysis.available_ram_gb:.1f}GB available")
        print(f"   CPU: {analysis.cpu_cores} cores @ {analysis.cpu_frequency_mhz:.0f}MHz")
        print(f"   GPU: {analysis.gpu_name}")
        if analysis.gpu_memory_gb > 0:
            print(f"        {analysis.gpu_memory_gb:.1f}GB VRAM, Compute {analysis.gpu_compute_capability}")
        
        # Current allocation
        print(f"\n📊 Current Resource Allocation:")
        print(f"   RAM Limit: {analysis.current_ram_limit_gb:.1f}GB ({analysis.current_ram_utilization*100:.1f}% of available)")
        print(f"   GPU Limit: {analysis.current_gpu_limit_gb:.1f}GB ({analysis.current_gpu_utilization*100:.1f}% of available)")
        
        # Optimization recommendations
        print(f"\n🚀 Optimization Recommendations:")
        print(f"   Recommended RAM: {analysis.recommended_ram_gb:.1f}GB ({analysis.recommended_ram_gb/analysis.available_ram_gb*100:.1f}% of available)")
        print(f"   Recommended GPU: {analysis.recommended_gpu_gb:.1f}GB ({analysis.recommended_gpu_gb/analysis.gpu_memory_gb*100:.1f}% of available)")
        
        # Performance impact
        print(f"\n📈 Performance Impact:")
        print(f"   Optimization Potential: {analysis.optimization_potential:.1f}% resource increase")
        print(f"   Estimated Performance Gain: {analysis.performance_gain_estimate:.1f}%")
        
        # Recommendations
        self._print_recommendations(analysis)
    
    def _print_recommendations(self, analysis: SystemAnalysis):
        """Print specific optimization recommendations"""
        print(f"\n💡 Specific Recommendations:")
        
        if analysis.optimization_potential > 10:
            print(f"   ✅ RECOMMENDED: Apply resource optimization")
            print(f"      - Increase RAM allocation by {analysis.recommended_ram_gb - analysis.current_ram_limit_gb:.1f}GB")
            print(f"      - Increase GPU allocation by {analysis.recommended_gpu_gb - analysis.current_gpu_limit_gb:.1f}GB")
        else:
            print(f"   ✅ OPTIMAL: Current allocation is already well-optimized")
        
        if "RTX 3070" in analysis.gpu_name:
            print(f"   🎮 RTX 3070 DETECTED: Using hardware-specific optimizations")
            print(f"      - Tensor Core acceleration enabled")
            print(f"      - Mixed precision training recommended")
        
        if analysis.available_ram_gb > 20:
            print(f"   💾 HIGH MEMORY SYSTEM: Enable advanced features")
            print(f"      - Large batch processing")
            print(f"      - Extended audio caching")
            print(f"      - Parallel preprocessing")


def main():
    """Main optimization analysis"""
    print("🚀 Phase 6 Resource Optimization Analysis")
    print("=" * 50)
    
    optimizer = ResourceOptimizer()
    
    try:
        # Perform system analysis
        print("🔍 Analyzing system resources...")
        analysis = optimizer.analyze_system()
        
        # Print detailed analysis
        optimizer.print_analysis(analysis)
        
        # Save analysis to file
        output_dir = Path("outputs/resource_analysis")
        output_dir.mkdir(parents=True, exist_ok=True)
        
        analysis_file = output_dir / "resource_optimization_analysis.json"
        with open(analysis_file, 'w') as f:
            json.dump(asdict(analysis), f, indent=2)
        
        print(f"\n📄 Analysis saved to: {analysis_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ Analysis failed: {e}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
