#!/usr/bin/env python3
"""
Test Optimized Configuration
"""

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.model.tja_generator import TJAGeneratorModel
from src.model import PHASE_3_MODEL_CONFIG
from src.utils.memory_monitor import MemoryMonitor
import torch

def main():
    print('🔧 Testing Optimized Configuration...')

    monitor = MemoryMonitor()
    monitor.log_memory_status('Before model creation')

    config = PHASE_3_MODEL_CONFIG['architecture']
    model = TJAGeneratorModel(config)

    if torch.cuda.is_available():
        model = model.cuda()

    monitor.log_memory_status('After model creation')

    # Test with optimized batch size
    optimal_batch = monitor.find_optimal_batch_size(
        max_sequence_length=config['max_sequence_length'],
        hidden_dim=config['hidden_dims']
    )

    print(f'🎯 Optimal batch size: {optimal_batch}')

    # Test forward pass
    batch_size = min(optimal_batch, 2)  # Use configured batch size
    audio_len = config['max_sequence_length']
    seq_len = config['max_sequence_length'] // 2

    audio_features = torch.randn(batch_size, audio_len, 201)
    target_sequence = torch.randint(0, 8, (batch_size, seq_len))
    target_timing = torch.randint(0, 16, (batch_size, seq_len))
    difficulty = torch.randint(0, 3, (batch_size,))

    if torch.cuda.is_available():
        audio_features = audio_features.cuda()
        target_sequence = target_sequence.cuda()
        target_timing = target_timing.cuda()
        difficulty = difficulty.cuda()

    model.train()
    outputs = model(
        audio_features=audio_features,
        target_sequence=target_sequence,
        target_timing=target_timing,
        difficulty=difficulty,
        return_loss=True
    )

    monitor.log_memory_status('After forward pass')

    model_info = model.get_model_size()
    print(f'📊 Optimized Model Stats:')
    print(f'  Parameters: {model_info["total_parameters"]:,}')
    print(f'  Size: {model_info["model_size_mb"]:.1f} MB')
    print(f'  Loss: {outputs["total_loss"].item():.4f}')

    print('✅ Optimized configuration validated!')

if __name__ == "__main__":
    main()
