#!/usr/bin/env python3
"""
Test Optimized Pipeline

Test the optimized TJA generation pipeline with enhanced GPU utilization
"""

import sys
import time
import torch
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.phase4.optimized_pipeline import OptimizedTJAGenerationPipeline
from src.utils.memory_monitor import MemoryMonitor

def test_optimized_pipeline():
    """Test the optimized pipeline"""
    print("🚀 Testing Optimized TJA Generation Pipeline")
    print("=" * 60)
    
    # Initialize memory monitor
    memory_monitor = MemoryMonitor()
    memory_monitor.log_memory_status("Before optimization test")
    
    try:
        # Initialize optimized pipeline
        print("🔧 Initializing optimized pipeline...")
        pipeline = OptimizedTJAGenerationPipeline()
        
        start_time = time.time()
        if not pipeline.initialize():
            print("❌ Pipeline initialization failed")
            return False
        
        init_time = time.time() - start_time
        print(f"✅ Pipeline initialized in {init_time:.2f}s")
        
        # Get optimization stats
        opt_stats = pipeline.get_optimization_stats()
        
        print("\n📊 Optimization Statistics:")
        print(f"  Optimal batch size: {opt_stats['performance_stats']['optimal_batch_size']}")
        print(f"  Mixed precision: {opt_stats['optimization_settings']['mixed_precision']}")
        print(f"  Dynamic batching: {opt_stats['optimization_settings']['dynamic_batching']}")
        print(f"  GPU memory target: {opt_stats['optimization_settings']['max_gpu_memory_fraction']*100:.0f}%")
        
        if torch.cuda.is_available():
            print(f"  GPU: {opt_stats['hardware_info']['gpu_name']}")
            print(f"  GPU memory: {opt_stats['hardware_info']['gpu_memory_total_gb']:.1f}GB")
        
        # Test synthetic generation with optimized settings
        print("\n🎵 Testing optimized generation...")
        
        # Create synthetic audio features with longer sequence
        seq_len = 800  # Longer sequence to test optimization
        feature_dim = 201
        audio_features = torch.randn(seq_len, feature_dim)
        
        print(f"  Audio features: {audio_features.shape}")
        
        # Test optimized sequence generation
        device = next(pipeline.model.parameters()).device
        audio_features_gpu = audio_features.to(device)
        
        generation_start = time.time()
        
        # Test all difficulties
        sequences = pipeline._generate_sequences_optimized(audio_features_gpu, [8, 9, 10])
        
        generation_time = time.time() - generation_start
        
        print(f"✅ Generated {len(sequences)} sequences in {generation_time:.2f}s")
        
        # Analyze results
        for difficulty, sequence in sequences.items():
            print(f"  Difficulty {difficulty}: {len(sequence)} notes")
        
        # Get updated optimization stats
        final_stats = pipeline.get_optimization_stats()
        
        print("\n📈 Performance Results:")
        print(f"  Peak GPU usage: {final_stats['performance_stats']['peak_memory_usage_gb']:.2f}GB")
        print(f"  GPU utilization: {final_stats['performance_stats']['peak_memory_usage_gb']/8*100:.1f}%")
        print(f"  Throughput: {final_stats['performance_stats']['throughput_sequences_per_second']:.1f} seq/s")
        print(f"  Avg generation time: {final_stats['performance_stats']['average_generation_time']:.3f}s")
        
        # Memory efficiency analysis
        memory_efficiency = (final_stats['performance_stats']['peak_memory_usage_gb'] / 8.0) * 100
        
        print(f"\n💾 Memory Efficiency Analysis:")
        print(f"  VRAM used: {final_stats['performance_stats']['peak_memory_usage_gb']:.2f}GB / 8.0GB")
        print(f"  Efficiency: {memory_efficiency:.1f}%")
        
        if memory_efficiency < 50:
            print("  ⚠️  Low GPU utilization - consider increasing batch size or model complexity")
        elif memory_efficiency > 90:
            print("  ⚠️  High GPU utilization - close to memory limits")
        else:
            print("  ✅ Good GPU utilization balance")
        
        # Test quality assessment
        print("\n🔍 Testing quality assessment...")
        
        sample_sequence = sequences[9]  # Use difficulty 9
        quality_metrics = pipeline.quality_assessor.evaluate_sequence(
            sample_sequence.numpy(), 
            audio_features.numpy(),
            difficulty_level=9
        )
        
        print(f"  Overall quality: {quality_metrics.overall_score:.3f}")
        print(f"  Musical accuracy: {quality_metrics.musical_accuracy:.3f}")
        print(f"  Difficulty appropriateness: {quality_metrics.difficulty_appropriateness:.3f}")
        print(f"  Playability: {quality_metrics.playability_score:.3f}")
        
        # Cleanup
        pipeline.cleanup()
        
        print("\n🎉 Optimized pipeline test completed successfully!")
        
        # Final memory status
        memory_monitor.log_memory_status("After optimization test")
        
        return True
        
    except Exception as e:
        print(f"❌ Optimized pipeline test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def benchmark_comparison():
    """Compare standard vs optimized pipeline performance"""
    print("\n🏁 Performance Comparison: Standard vs Optimized")
    print("=" * 60)
    
    # Test parameters
    seq_len = 400
    difficulties = [8, 9, 10]
    
    results = {}
    
    # Test standard pipeline
    print("📊 Testing standard pipeline...")
    try:
        from src.phase4.pipeline import TJAGenerationPipeline
        
        standard_pipeline = TJAGenerationPipeline()
        if standard_pipeline.initialize():
            
            # Create test data
            audio_features = torch.randn(seq_len, 201)
            device = next(standard_pipeline.model.parameters()).device
            audio_features_gpu = audio_features.to(device)
            
            # Time standard generation
            start_time = time.time()
            standard_sequences = standard_pipeline._generate_sequences(audio_features_gpu, difficulties)
            standard_time = time.time() - start_time
            
            results["standard"] = {
                "time": standard_time,
                "sequences": len(standard_sequences),
                "throughput": len(standard_sequences) / standard_time
            }
            
            print(f"  ✅ Standard: {standard_time:.2f}s, {results['standard']['throughput']:.1f} seq/s")
            
            standard_pipeline.cleanup()
        else:
            print("  ❌ Standard pipeline initialization failed")
            
    except Exception as e:
        print(f"  ❌ Standard pipeline test failed: {e}")
    
    # Test optimized pipeline
    print("📊 Testing optimized pipeline...")
    try:
        optimized_pipeline = OptimizedTJAGenerationPipeline()
        if optimized_pipeline.initialize():
            
            # Create test data
            audio_features = torch.randn(seq_len, 201)
            device = next(optimized_pipeline.model.parameters()).device
            audio_features_gpu = audio_features.to(device)
            
            # Time optimized generation
            start_time = time.time()
            optimized_sequences = optimized_pipeline._generate_sequences_optimized(audio_features_gpu, difficulties)
            optimized_time = time.time() - start_time
            
            results["optimized"] = {
                "time": optimized_time,
                "sequences": len(optimized_sequences),
                "throughput": len(optimized_sequences) / optimized_time
            }
            
            print(f"  ✅ Optimized: {optimized_time:.2f}s, {results['optimized']['throughput']:.1f} seq/s")
            
            optimized_pipeline.cleanup()
        else:
            print("  ❌ Optimized pipeline initialization failed")
            
    except Exception as e:
        print(f"  ❌ Optimized pipeline test failed: {e}")
    
    # Compare results
    if "standard" in results and "optimized" in results:
        speedup = results["standard"]["time"] / results["optimized"]["time"]
        throughput_improvement = (results["optimized"]["throughput"] / results["standard"]["throughput"] - 1) * 100
        
        print(f"\n📈 Performance Improvement:")
        print(f"  Speedup: {speedup:.2f}x")
        print(f"  Throughput improvement: {throughput_improvement:+.1f}%")
        
        if speedup > 1.5:
            print("  🚀 Significant performance improvement!")
        elif speedup > 1.1:
            print("  ✅ Good performance improvement")
        else:
            print("  ⚠️  Minimal performance improvement")

def main():
    """Main test function"""
    success = test_optimized_pipeline()
    
    if success:
        benchmark_comparison()
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
