#!/usr/bin/env python3
"""
Memory-Optimized Test Script for Phase 3 Implementation
Tests the deep learning model with memory constraints and optimizations
"""

import sys
import time
import torch
import numpy as np
import gc
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.model.tja_generator import TJAGeneratorModel
from src.model import PHASE_3_MODEL_CONFIG
from src.utils.memory_monitor import MemoryMonitor, MemoryContext, setup_memory_optimized_environment


def setup_memory_optimized_testing():
    """Setup memory-optimized environment for testing"""
    print("🔧 Setting up memory-optimized testing environment...")
    
    # Setup memory optimization
    monitor = setup_memory_optimized_environment()
    
    # Force garbage collection
    gc.collect()
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    
    monitor.log_memory_status("Test Setup")
    return monitor


def test_memory_monitor():
    """Test memory monitoring functionality"""
    print("💾 Testing Memory Monitor...")
    
    try:
        monitor = MemoryMonitor()
        
        # Test memory stats
        stats = monitor.get_memory_stats()
        print(f"  ✅ Memory stats retrieved:")
        print(f"    GPU: {stats.gpu_reserved_gb:.2f}GB / {stats.gpu_total_gb:.2f}GB")
        print(f"    System: {stats.system_memory_percent:.1f}%")
        
        # Test memory pressure check
        is_pressure, reason = monitor.check_memory_pressure()
        print(f"  ✅ Memory pressure check: {reason}")
        
        # Test optimization suggestions
        suggestions = monitor.suggest_optimizations()
        if suggestions:
            print(f"  💡 Optimization suggestions:")
            for key, suggestion in suggestions.items():
                print(f"    {key}: {suggestion}")
        
        # Test optimal batch size calculation
        optimal_batch = monitor.find_optimal_batch_size(
            max_sequence_length=500, hidden_dim=128
        )
        print(f"  🎯 Optimal batch size: {optimal_batch}")
        
        print(f"  ✅ Memory monitor test PASSED")
        return True
        
    except Exception as e:
        print(f"  ❌ Memory monitor test FAILED: {e}")
        return False


def test_small_model():
    """Test model with very small configuration"""
    print("🤖 Testing Small Model Configuration...")
    
    try:
        # Create minimal config for testing
        small_config = {
            "audio_feature_dims": 201,
            "hidden_dims": 32,          # Very small but divisible by heads
            "num_attention_heads": 2,   # Minimal
            "num_encoder_layers": 1,    # Single layer
            "num_decoder_layers": 1,    # Single layer
            "dropout": 0.1,
            "max_sequence_length": 100, # Very short
            "note_types": 8,
            "difficulty_levels": 3,
            "pattern_context_dims": 8   # Very small but divisible
        }
        
        monitor = MemoryMonitor()
        
        with MemoryContext(monitor, "small_model_creation"):
            model = TJAGeneratorModel(small_config)
        
        # Test forward pass with minimal data
        batch_size = 1
        audio_len = 50
        seq_len = 25
        
        with MemoryContext(monitor, "small_model_forward"):
            audio_features = torch.randn(batch_size, audio_len, 201)
            target_sequence = torch.randint(0, 8, (batch_size, seq_len))
            target_timing = torch.randint(0, 16, (batch_size, seq_len))
            difficulty = torch.randint(0, 3, (batch_size,))
            
            if torch.cuda.is_available():
                model = model.cuda()
                audio_features = audio_features.cuda()
                target_sequence = target_sequence.cuda()
                target_timing = target_timing.cuda()
                difficulty = difficulty.cuda()
            
            outputs = model(
                audio_features=audio_features,
                target_sequence=target_sequence,
                target_timing=target_timing,
                difficulty=difficulty,
                return_loss=True
            )
        
        print(f"  ✅ Small model test completed:")
        print(f"    Model parameters: {sum(p.numel() for p in model.parameters()):,}")
        print(f"    Output shape: {outputs['logits'].shape}")
        print(f"    Loss: {outputs['total_loss'].item():.4f}")
        
        # Memory cleanup
        del model, outputs, audio_features, target_sequence, target_timing, difficulty
        gc.collect()
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        print(f"  ✅ Small model test PASSED")
        return True
        
    except Exception as e:
        print(f"  ❌ Small model test FAILED: {e}")
        return False


def test_memory_optimized_model():
    """Test the memory-optimized model configuration"""
    print("🎯 Testing Memory-Optimized Model...")
    
    try:
        config = PHASE_3_MODEL_CONFIG["architecture"]
        monitor = MemoryMonitor()
        
        # Log initial memory
        monitor.log_memory_status("Before model creation")
        
        with MemoryContext(monitor, "optimized_model_creation"):
            model = TJAGeneratorModel(config)
        
        # Get model info
        model_info = model.get_model_size()
        print(f"  📊 Model info:")
        print(f"    Parameters: {model_info['total_parameters']:,}")
        print(f"    Size: {model_info['model_size_mb']:.1f} MB")
        
        # Test with optimized batch size
        optimal_batch = monitor.find_optimal_batch_size(
            max_sequence_length=config["max_sequence_length"],
            hidden_dim=config["hidden_dims"]
        )
        
        print(f"  🎯 Using optimal batch size: {optimal_batch}")
        
        # Test forward pass
        audio_len = config["max_sequence_length"]
        seq_len = config["max_sequence_length"] // 2
        
        with MemoryContext(monitor, "optimized_model_forward"):
            audio_features = torch.randn(optimal_batch, audio_len, 201)
            target_sequence = torch.randint(0, 8, (optimal_batch, seq_len))
            target_timing = torch.randint(0, 16, (optimal_batch, seq_len))
            difficulty = torch.randint(0, 3, (optimal_batch,))
            
            if torch.cuda.is_available():
                model = model.cuda()
                audio_features = audio_features.cuda()
                target_sequence = target_sequence.cuda()
                target_timing = target_timing.cuda()
                difficulty = difficulty.cuda()
            
            # Enable gradient checkpointing
            model.train()
            
            outputs = model(
                audio_features=audio_features,
                target_sequence=target_sequence,
                target_timing=target_timing,
                difficulty=difficulty,
                return_loss=True
            )
        
        print(f"  ✅ Forward pass completed:")
        print(f"    Input shape: {audio_features.shape}")
        print(f"    Output shape: {outputs['logits'].shape}")
        print(f"    Loss: {outputs['total_loss'].item():.4f}")
        
        # Test generation
        with MemoryContext(monitor, "optimized_model_generation"):
            model.eval()
            with torch.no_grad():
                generated = model.generate(
                    audio_features=audio_features[:1],  # Single sample
                    difficulty=1,
                    temperature=1.0
                )
        
        if "generated_sequence" in generated:
            print(f"  ✅ Generation completed: {generated['generated_sequence'].shape}")
        
        # Final memory status
        monitor.log_memory_status("After testing")
        
        # Memory cleanup
        del model, outputs, audio_features, target_sequence, target_timing, difficulty
        gc.collect()
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        print(f"  ✅ Memory-optimized model test PASSED")
        return True
        
    except RuntimeError as e:
        if "out of memory" in str(e).lower():
            print(f"  ❌ Out of memory error: {e}")
            monitor.handle_oom_error(e)
            return False
        else:
            raise e
    except Exception as e:
        print(f"  ❌ Memory-optimized model test FAILED: {e}")
        return False


def test_gradient_checkpointing():
    """Test gradient checkpointing functionality"""
    print("🔄 Testing Gradient Checkpointing...")
    
    try:
        # Small config for testing
        config = {
            "audio_feature_dims": 201,
            "hidden_dims": 64,         # Smaller for memory
            "num_attention_heads": 2,  # Reduced
            "num_encoder_layers": 2,
            "num_decoder_layers": 2,
            "dropout": 0.1,
            "max_sequence_length": 200,
            "note_types": 8,
            "difficulty_levels": 3,
            "pattern_context_dims": 16  # Smaller
        }
        
        monitor = MemoryMonitor()
        model = TJAGeneratorModel(config)
        
        if torch.cuda.is_available():
            model = model.cuda()
        
        # Test with gradient checkpointing enabled
        model.train()
        
        batch_size = 1
        audio_len = 200
        seq_len = 100
        
        audio_features = torch.randn(batch_size, audio_len, 201, requires_grad=True)
        target_sequence = torch.randint(0, 8, (batch_size, seq_len))
        target_timing = torch.randint(0, 16, (batch_size, seq_len))
        difficulty = torch.randint(0, 3, (batch_size,))
        
        if torch.cuda.is_available():
            audio_features = audio_features.cuda()
            target_sequence = target_sequence.cuda()
            target_timing = target_timing.cuda()
            difficulty = difficulty.cuda()
        
        with MemoryContext(monitor, "gradient_checkpointing_forward"):
            outputs = model(
                audio_features=audio_features,
                target_sequence=target_sequence,
                target_timing=target_timing,
                difficulty=difficulty,
                return_loss=True
            )
            
            loss = outputs["total_loss"]
        
        with MemoryContext(monitor, "gradient_checkpointing_backward"):
            loss.backward()
        
        print(f"  ✅ Gradient checkpointing test completed:")
        print(f"    Loss: {loss.item():.4f}")
        print(f"    Gradients computed successfully")
        
        # Check if gradients exist
        has_gradients = any(p.grad is not None for p in model.parameters() if p.requires_grad)
        print(f"    Gradients present: {has_gradients}")
        
        # Memory cleanup
        del model, outputs, audio_features, target_sequence, target_timing, difficulty, loss
        gc.collect()
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        print(f"  ✅ Gradient checkpointing test PASSED")
        return True
        
    except Exception as e:
        print(f"  ❌ Gradient checkpointing test FAILED: {e}")
        return False


def test_memory_cleanup():
    """Test memory cleanup functionality"""
    print("🧹 Testing Memory Cleanup...")
    
    try:
        monitor = MemoryMonitor()
        
        # Get initial memory
        initial_stats = monitor.get_memory_stats()
        print(f"  📊 Initial GPU memory: {initial_stats.gpu_reserved_gb:.2f}GB")
        
        # Create some tensors to use memory
        tensors = []
        for i in range(10):
            tensor = torch.randn(100, 100, 100)
            if torch.cuda.is_available():
                tensor = tensor.cuda()
            tensors.append(tensor)
        
        # Check memory after allocation
        after_alloc_stats = monitor.get_memory_stats()
        print(f"  📊 After allocation: {after_alloc_stats.gpu_reserved_gb:.2f}GB")
        
        # Delete tensors
        del tensors
        
        # Test cleanup
        monitor.cleanup_memory(aggressive=True)
        
        # Check memory after cleanup
        after_cleanup_stats = monitor.get_memory_stats()
        print(f"  📊 After cleanup: {after_cleanup_stats.gpu_reserved_gb:.2f}GB")
        
        # Verify memory was freed
        memory_freed = after_alloc_stats.gpu_reserved_gb - after_cleanup_stats.gpu_reserved_gb
        print(f"  ✅ Memory freed: {memory_freed:.2f}GB")
        
        print(f"  ✅ Memory cleanup test PASSED")
        return True
        
    except Exception as e:
        print(f"  ❌ Memory cleanup test FAILED: {e}")
        return False


def main():
    """Run all memory-optimized Phase 3 tests"""
    print("🧪 TJA Generator Phase 3 - Memory-Optimized Test Suite")
    print("=" * 60)
    
    # Setup memory-optimized environment
    monitor = setup_memory_optimized_testing()
    
    tests = [
        ("Memory Monitor", test_memory_monitor),
        ("Small Model", test_small_model),
        ("Memory-Optimized Model", test_memory_optimized_model),
        ("Gradient Checkpointing", test_gradient_checkpointing),
        ("Memory Cleanup", test_memory_cleanup)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        try:
            # Monitor memory for each test
            with MemoryContext(monitor, test_name.lower().replace(" ", "_")):
                if test_func():
                    passed += 1
                    print(f"  ✅ {test_name} PASSED")
                else:
                    print(f"  ❌ {test_name} FAILED")
        except Exception as e:
            print(f"  💥 {test_name} CRASHED: {e}")
        
        # Cleanup between tests
        gc.collect()
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    # Final memory summary
    print(f"\n💾 Final Memory Summary:")
    memory_summary = monitor.get_memory_summary()
    for key, value in memory_summary.items():
        print(f"  {key}: {value}")
    
    if passed == total:
        print("🎉 All memory-optimized tests passed! Phase 3 is ready for training.")
        return 0
    else:
        print("⚠️  Some tests failed. Memory optimizations may need adjustment.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
