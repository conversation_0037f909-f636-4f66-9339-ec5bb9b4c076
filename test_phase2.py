#!/usr/bin/env python3
"""
Test Script for Phase 2 Implementation
Validates audio feature extraction functionality before full processing
"""

import sys
import time
import torch
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.audio_processing.feature_extractor import AudioFeatureExtractor
from src.audio_processing.spectral_processor import SpectralProcessor
from src.audio_processing.rhythmic_processor import RhythmicProcessor
from src.audio_processing.temporal_aligner import TemporalAligner
from src.audio_processing.gpu_optimizer import RTX3070Optimizer
from src.validation.feature_validator import FeatureValidator
from src.validation.alignment_validator import AlignmentValidator


def test_gpu_optimizer():
    """Test GPU optimizer functionality"""
    print("🖥️  Testing GPU Optimizer...")
    
    try:
        if not torch.cuda.is_available():
            print("  ⚠️  CUDA not available - skipping GPU tests")
            return True
        
        optimizer = RTX3070Optimizer()
        print(f"  ✅ GPU initialized: {optimizer.get_config()['device_name']}")
        
        # Test memory monitoring
        memory_stats = optimizer.get_memory_stats()
        print(f"  📊 GPU Memory: {memory_stats.allocated_gb:.1f}GB allocated, {memory_stats.utilization_percent:.1f}% utilization")
        
        # Test batch size optimization
        optimized_batch = optimizer.optimize_batch_size(4, 1000)
        print(f"  🔧 Optimized batch size: {optimized_batch}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ GPU optimizer test failed: {e}")
        return False


def test_spectral_processor():
    """Test spectral feature extraction"""
    print("🎵 Testing Spectral Processor...")
    
    try:
        processor = SpectralProcessor()
        
        # Create test audio (1 second at 44.1kHz)
        sample_rate = 44100
        duration = 1.0
        test_audio = torch.randn(int(sample_rate * duration))
        
        # Test individual features
        mel_spec = processor.extract_mel_spectrogram(test_audio)
        mfcc = processor.extract_mfcc(test_audio)
        chroma = processor.extract_chroma(test_audio)
        
        print(f"  ✅ Mel-spectrogram: {mel_spec.shape}")
        print(f"  ✅ MFCC: {mfcc.shape}")
        print(f"  ✅ Chroma: {chroma.shape}")
        
        # Test combined features
        all_features = processor.extract_all_spectral_features(test_audio)
        combined = all_features["combined_spectral"]
        
        print(f"  ✅ Combined spectral features: {combined.shape}")
        print(f"  📊 Expected dimensions: [T, 153] (128+13+12)")
        
        # Validate features
        validation = processor.validate_spectral_features(combined)
        print(f"  🔍 Validation passed: {validation.get('all_features_valid', False)}")
        
        return combined.shape[1] == 153  # 128 mel + 13 mfcc + 12 chroma
        
    except Exception as e:
        print(f"  ❌ Spectral processor test failed: {e}")
        return False


def test_rhythmic_processor():
    """Test rhythmic feature extraction"""
    print("🥁 Testing Rhythmic Processor...")
    
    try:
        processor = RhythmicProcessor()
        
        # Create test audio with some rhythmic content
        sample_rate = 44100
        duration = 2.0
        t = torch.linspace(0, duration, int(sample_rate * duration))
        
        # Create a simple beat pattern (120 BPM)
        beat_freq = 2.0  # 2 Hz = 120 BPM
        test_audio = torch.sin(2 * torch.pi * 440 * t) * torch.sin(2 * torch.pi * beat_freq * t)
        test_audio_np = test_audio.numpy()
        
        # Test rhythmic features
        rhythmic_features = processor.extract_all_rhythmic_features(test_audio_np)
        
        features_tensor = rhythmic_features["rhythmic_features"]
        print(f"  ✅ Rhythmic features: {features_tensor.shape}")
        print(f"  📊 Expected dimensions: [T, 32]")
        
        # Check onset detection
        onset_info = rhythmic_features["feature_info"]
        print(f"  🎯 Onset count: {onset_info['onset_count']}")
        print(f"  🎵 Beat count: {onset_info['beat_count']}")
        
        return features_tensor.shape[1] == 32
        
    except Exception as e:
        print(f"  ❌ Rhythmic processor test failed: {e}")
        return False


def test_temporal_aligner():
    """Test temporal alignment functionality"""
    print("⏰ Testing Temporal Aligner...")
    
    try:
        aligner = TemporalAligner()
        
        # Test timing grid creation
        audio_duration = 10.0  # 10 seconds
        base_bpm = 120.0
        offset = -0.5  # 500ms offset
        
        timing_grid = aligner.create_timing_grid(audio_duration, base_bpm, offset)
        
        print(f"  ✅ Timing grid created")
        print(f"  📊 Time frames: {timing_grid['beat_grid'].shape[0]}")
        print(f"  🎵 Beat positions: {len(timing_grid['beat_positions'])}")
        print(f"  📏 Measure positions: {len(timing_grid['measure_positions'])}")
        
        # Test feature alignment
        test_features = torch.randn(500, 201)  # 10 seconds at 50fps
        note_timings = [1000, 2000, 3000, 4000, 5000]  # Every second
        
        alignment_result = aligner.align_features_to_notes(
            test_features, note_timings, timing_grid
        )
        
        print(f"  ✅ Feature alignment completed")
        print(f"  🎯 Aligned features: {alignment_result['aligned_features'].shape}")
        print(f"  📊 Mean alignment error: {alignment_result['alignment_stats']['mean_error_ms']:.1f}ms")
        
        return alignment_result['alignment_stats']['alignment_accuracy'] > 0.8
        
    except Exception as e:
        print(f"  ❌ Temporal aligner test failed: {e}")
        return False


def test_feature_extractor():
    """Test complete feature extraction pipeline"""
    print("🎼 Testing Complete Feature Extractor...")
    
    # Find a sample audio file
    sample_audio = None
    audio_extensions = ['.ogg', '.mp3', '.wav']
    
    for ext in audio_extensions:
        audio_files = list(Path("data/raw/ese").rglob(f"*{ext}"))
        if audio_files:
            sample_audio = str(audio_files[0])
            break
    
    if not sample_audio:
        print("  ⚠️  No sample audio file found - creating synthetic audio")
        
        # Create synthetic audio file for testing
        import torchaudio
        sample_rate = 44100
        duration = 5.0
        t = torch.linspace(0, duration, int(sample_rate * duration))
        
        # Create a more complex test signal
        audio = (torch.sin(2 * torch.pi * 440 * t) * 0.3 +  # A4 note
                torch.sin(2 * torch.pi * 880 * t) * 0.2 +   # A5 note
                torch.sin(2 * torch.pi * 220 * t) * 0.1)    # A3 note
        
        # Add some rhythmic modulation
        beat_pattern = torch.sin(2 * torch.pi * 2 * t)  # 120 BPM
        audio = audio * (0.7 + 0.3 * beat_pattern)
        
        # Save temporary audio file
        temp_audio_path = Path("temp_test_audio.wav")
        torchaudio.save(temp_audio_path, audio.unsqueeze(0), sample_rate)
        sample_audio = str(temp_audio_path)
        print(f"  📄 Created synthetic audio: {sample_audio}")
    else:
        print(f"  📄 Using sample audio: {Path(sample_audio).name}")
    
    try:
        extractor = AudioFeatureExtractor()
        
        # Test metadata
        tja_metadata = {
            "base_bpm": 120.0,
            "offset": 0.0,
            "timing_commands": []
        }
        
        # Extract features
        start_time = time.time()
        features = extractor.extract_features(sample_audio, tja_metadata)
        extraction_time = time.time() - start_time
        
        print(f"  ✅ Feature extraction completed in {extraction_time:.2f}s")
        
        # Check main output
        combined_features = features["combined_features"]
        print(f"  🎯 Combined features shape: {combined_features.shape}")
        print(f"  📊 Expected: [T, 201]")
        
        # Check individual components
        feature_metadata = features["feature_metadata"]
        print(f"  📏 Time frames: {feature_metadata['time_frames']}")
        print(f"  🎵 Spectral dims: {feature_metadata['spectral_dims']}")
        print(f"  🥁 Rhythmic dims: {feature_metadata['rhythmic_dims']}")
        print(f"  ⏰ Temporal dims: {feature_metadata['temporal_dims']}")
        
        # Cleanup temporary file if created
        if "temp_test_audio.wav" in sample_audio:
            Path(sample_audio).unlink()
        
        return combined_features.shape[1] == 201
        
    except Exception as e:
        print(f"  ❌ Feature extractor test failed: {e}")
        # Cleanup temporary file if created
        if sample_audio and "temp_test_audio.wav" in sample_audio:
            Path(sample_audio).unlink(missing_ok=True)
        return False


def test_feature_validator():
    """Test feature validation functionality"""
    print("✅ Testing Feature Validator...")
    
    try:
        validator = FeatureValidator()
        
        # Create test features
        test_features = {
            "combined_features": torch.randn(100, 201),
            "spectral_features": torch.randn(100, 153),
            "rhythmic_features": torch.randn(100, 32),
            "temporal_features": torch.randn(100, 16)
        }
        
        # Test validation
        validation_result = validator.validate_complete_features(test_features)
        
        print(f"  ✅ Validation completed")
        print(f"  🎯 Features valid: {validation_result.valid}")
        print(f"  📊 Quality score: {validation_result.quality_score:.2f}")
        print(f"  ⚠️  Warnings: {len(validation_result.warnings)}")
        print(f"  ❌ Errors: {len(validation_result.errors)}")
        
        return validation_result.quality_score > 0.5
        
    except Exception as e:
        print(f"  ❌ Feature validator test failed: {e}")
        return False


def test_alignment_validator():
    """Test alignment validation functionality"""
    print("🎯 Testing Alignment Validator...")
    
    try:
        validator = AlignmentValidator()
        
        # Create test timing grid
        time_frames = 500  # 10 seconds at 50fps
        timing_grid = {
            "beat_grid": torch.zeros(time_frames),
            "measure_grid": torch.zeros(time_frames),
            "time_axis": torch.linspace(0, 10, time_frames),
            "bpm_sequence": torch.full((time_frames,), 120.0)
        }
        
        # Add some beats
        beat_interval = 25  # 50fps / 2 = 25 frames per beat at 120 BPM
        for i in range(0, time_frames, beat_interval):
            if i < time_frames:
                timing_grid["beat_grid"][i] = 1.0
        
        # Test timing grid validation
        grid_validation = validator.validate_timing_grid(timing_grid, 10.0, 120.0)
        
        print(f"  ✅ Timing grid validation completed")
        print(f"  🎯 Grid valid: {grid_validation.valid}")
        print(f"  📊 Alignment accuracy: {grid_validation.alignment_accuracy:.2f}")
        
        # Test feature-note alignment
        test_features = torch.randn(time_frames, 201)
        note_timings = [1000, 2000, 3000, 4000, 5000]  # Every second in ms
        
        alignment_validation = validator.validate_feature_note_alignment(
            test_features, note_timings, timing_grid
        )
        
        print(f"  ✅ Feature-note alignment validation completed")
        print(f"  🎯 Alignment valid: {alignment_validation.valid}")
        print(f"  📊 Mean error: {alignment_validation.mean_error_ms:.1f}ms")
        
        return grid_validation.alignment_accuracy > 0.5
        
    except Exception as e:
        print(f"  ❌ Alignment validator test failed: {e}")
        return False


def main():
    """Run all Phase 2 tests"""
    print("🧪 TJA Generator Phase 2 - Test Suite")
    print("=" * 50)
    
    tests = [
        ("GPU Optimizer", test_gpu_optimizer),
        ("Spectral Processor", test_spectral_processor),
        ("Rhythmic Processor", test_rhythmic_processor),
        ("Temporal Aligner", test_temporal_aligner),
        ("Feature Extractor", test_feature_extractor),
        ("Feature Validator", test_feature_validator),
        ("Alignment Validator", test_alignment_validator)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        try:
            if test_func():
                passed += 1
                print(f"  ✅ {test_name} PASSED")
            else:
                print(f"  ❌ {test_name} FAILED")
        except Exception as e:
            print(f"  💥 {test_name} CRASHED: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Phase 2 implementation is ready.")
        return 0
    else:
        print("⚠️  Some tests failed. Please review the implementation.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
