#!/usr/bin/env python3
"""
Phase 6: Inference Pipeline and Validation - Main Interface

Complete inference pipeline for TJA chart generation with comprehensive validation,
performance benchmarking, and production deployment capabilities.
"""

import sys
import argparse
import logging
import time
import json
import numpy as np
import torch
from pathlib import Path
from typing import Dict, List, Optional, Any

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Phase 6 imports
from src.phase6 import (
    TJAInferenceSystem, create_phase6_config, validate_environment,
    PerformanceBenchmark, TJAValidator, Phase6Config
)
from src.utils.memory_monitor import MemoryMonitor
from src.utils.resource_manager import ResourceManager, cleanup_global_resources


def setup_logging(log_level: str = "INFO"):
    """Setup logging configuration"""
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('phase6.log')
        ]
    )


def validate_system() -> Dict[str, Any]:
    """Validate Phase 6 system and environment"""
    print("🔍 Phase 6 System Validation")
    print("=" * 40)
    
    # Environment validation
    print("📋 Validating environment...")
    env_results = validate_environment()
    
    print(f"   Environment valid: {'✅' if env_results['valid'] else '❌'}")
    
    if env_results["warnings"]:
        print("   Warnings:")
        for warning in env_results["warnings"]:
            print(f"     ⚠️  {warning}")
    
    if env_results["errors"]:
        print("   Errors:")
        for error in env_results["errors"]:
            print(f"     ❌ {error}")
    
    # Hardware validation
    if "hardware" in env_results:
        hardware = env_results["hardware"]
        if "gpu" in hardware:
            gpu = hardware["gpu"]
            print(f"   GPU: {gpu['name']} ({gpu['memory_gb']:.1f}GB)")
        else:
            print("   GPU: Not available")
    
    # Dependencies validation
    print("   Dependencies:")
    for dep, info in env_results["dependencies"].items():
        status = "✅" if info["available"] else "❌"
        description = info.get("description", "Core dependency")
        print(f"     {status} {dep}: {description}")

    # Resource validation
    print("\n💾 Resource Validation:")
    try:
        import psutil
        import torch

        # System memory
        memory = psutil.virtual_memory()
        memory_gb = memory.total / (1024**3)
        available_gb = memory.available / (1024**3)

        print(f"   System Memory: {memory_gb:.1f}GB total, {available_gb:.1f}GB available ({memory.percent:.1f}% used)")

        if available_gb < 2.0:
            print("     ⚠️  Low available memory - may cause issues")
            env_results["warnings"].append("Low available system memory")

        # GPU memory
        if torch.cuda.is_available():
            gpu_memory_gb = torch.cuda.get_device_properties(0).total_memory / (1024**3)
            print(f"   GPU Memory: {gpu_memory_gb:.1f}GB")

            if gpu_memory_gb < 2.0:
                print("     ⚠️  Limited GPU memory - will use CPU fallback")
                env_results["warnings"].append("Limited GPU memory")
        else:
            print("   GPU Memory: Not available")

        # CPU cores
        cpu_count = psutil.cpu_count()
        print(f"   CPU Cores: {cpu_count}")

        # Memory recommendations
        if memory_gb < 8.0:
            print("   📋 Memory Recommendations:")
            print("     • Close unnecessary applications")
            print("     • Use shorter audio files (< 3 minutes)")
            print("     • Enable CPU-only inference if needed")

    except Exception as e:
        print(f"   ❌ Resource validation failed: {e}")
        env_results["errors"].append(f"Resource validation failed: {e}")

    # Create test configuration
    print("\n🔧 Testing configuration system...")
    try:
        config = create_phase6_config(debug_mode=True)
        print("   ✅ Configuration system working")
        print(f"   Auto-detected memory limits: RAM {config.resource.max_system_memory_gb:.1f}GB, GPU {config.resource.max_gpu_memory_gb:.1f}GB")
    except Exception as e:
        print(f"   ❌ Configuration failed: {e}")
        env_results["valid"] = False
    
    # Test inference system initialization
    print("\n🧠 Testing inference system...")
    try:
        # Create minimal config for testing
        test_config = create_phase6_config(debug_mode=True)
        
        # Check if model exists
        model_path = Path(test_config.inference.model_path)
        if model_path.exists():
            print(f"   ✅ Model found: {model_path}")
        else:
            print(f"   ⚠️  Model not found: {model_path}")
            print("      Run Phase 5 training first or provide model path")
    
    except Exception as e:
        print(f"   ❌ Inference system test failed: {e}")
    
    # Save validation results
    output_dir = Path("outputs/phase6_validation")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    with open(output_dir / "system_validation.json", 'w') as f:
        json.dump(env_results, f, indent=2, default=str)
    
    print(f"\n📄 Validation results saved: {output_dir / 'system_validation.json'}")
    
    return env_results


def run_inference(args) -> Dict[str, Any]:
    """Run TJA inference on audio file"""
    print("🎵 Phase 6 TJA Inference")
    print("=" * 30)

    start_time = time.time()

    try:
        # Check audio file size first
        audio_path = Path(args.audio_file)
        if not audio_path.exists():
            raise FileNotFoundError(f"Audio file not found: {audio_path}")

        file_size_mb = audio_path.stat().st_size / (1024 * 1024)
        print(f"📁 Audio file: {audio_path.name} ({file_size_mb:.1f}MB)")

        if file_size_mb > 50:
            print("   ⚠️  Large audio file - may require significant memory")

        # Create configuration with resource constraints
        config = create_phase6_config(
            experiment_name=args.experiment_name,
            debug_mode=args.debug
        )

        # Override model path if provided
        if args.model_path:
            config.inference.model_path = args.model_path

        print(f"📊 Configuration:")
        print(f"   Model: {config.inference.model_path}")
        print(f"   Chunked inference: {config.inference.use_chunked_inference}")
        print(f"   Mixed precision: {config.inference.use_mixed_precision}")
        print(f"   Device: {config.inference.device}")
        print(f"   Memory limits: RAM {config.resource.max_system_memory_gb:.1f}GB, GPU {config.resource.max_gpu_memory_gb:.1f}GB")

        # Check resource availability
        print("\n💾 Checking resource availability...")
        resource_manager = ResourceManager()

        # Estimate required resources based on file size
        estimated_memory_gb = max(1.0, file_size_mb / 50.0)  # Rough estimate
        estimated_gpu_memory_gb = max(0.5, file_size_mb / 100.0) if torch.cuda.is_available() else 0.0

        available, message = resource_manager.monitor.check_resource_availability(
            estimated_memory_gb, estimated_gpu_memory_gb
        )

        if not available:
            print(f"   ❌ {message}")
            print("   💡 Recommendations:")
            print("     • Use a shorter audio file")
            print("     • Close other applications")
            print("     • Enable CPU-only inference")
            return {"success": False, "error": message}

        print(f"   ✅ Resources available (estimated need: {estimated_memory_gb:.1f}GB RAM, {estimated_gpu_memory_gb:.1f}GB GPU)")

        # Initialize inference system
        print("\n🔧 Initializing inference system...")
        inference_system = TJAInferenceSystem(config)
        
        # Run inference
        print(f"\n🎯 Processing audio: {args.audio_file}")
        result = inference_system.generate_chart(
            audio_path=args.audio_file,
            bpm=args.bpm,
            offset=args.offset,
            difficulty_level=args.difficulty,
            course_type=args.course_type
        )
        
        if result["success"]:
            # Save generated chart
            output_dir = Path(args.output_dir)
            output_dir.mkdir(parents=True, exist_ok=True)
            
            # Generate output filename
            audio_name = Path(args.audio_file).stem
            output_file = output_dir / f"{audio_name}_oni{args.difficulty}.tja"
            
            # Convert chart to TJA string and save
            from src.phase6.tja_postprocessing import TJAPostProcessor
            postprocessor = TJAPostProcessor(config.inference.postprocessing)
            tja_string = postprocessor.chart_to_tja_string(result["tja_chart"])
            
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(tja_string)
            
            # Save detailed results
            results_file = output_dir / f"{audio_name}_results.json"
            with open(results_file, 'w') as f:
                json.dump({
                    "performance_metrics": result["performance_metrics"],
                    "validation_results": result["validation_results"],
                    "chart_metadata": {
                        "note_count": len(result["tja_chart"].notes),
                        "measure_count": len(result["tja_chart"].measures),
                        "difficulty_level": result["tja_chart"].difficulty_level
                    }
                }, f, indent=2, default=str)
            
            # Print results
            perf = result["performance_metrics"]
            validation = result["validation_results"]
            
            print(f"\n✅ Inference completed successfully!")
            print(f"   Total time: {perf['total_time']:.2f}s")
            print(f"   Realtime factor: {perf['realtime_factor']:.1f}x")
            print(f"   Memory usage: {perf['memory_usage']['gpu_delta_gb']:.2f}GB")
            print(f"   Validation score: {validation['overall_score']:.3f}")
            print(f"   Chart saved: {output_file}")
            print(f"   Results saved: {results_file}")
            
            return result
            
        else:
            print(f"❌ Inference failed: {result['error']}")
            return result
    
    except Exception as e:
        print(f"❌ Inference failed: {e}")
        import traceback
        traceback.print_exc()
        return {"success": False, "error": str(e)}


def run_benchmark(args) -> Dict[str, Any]:
    """Run performance benchmark"""
    print("📊 Phase 6 Performance Benchmark")
    print("=" * 35)
    
    try:
        # Create configuration
        config = create_phase6_config(
            experiment_name=f"benchmark_{int(time.time())}",
            enable_benchmarking=True,
            debug_mode=args.debug
        )
        
        # Override benchmark settings
        config.benchmark.max_test_cases = args.max_test_cases
        config.benchmark.benchmark_iterations = args.iterations
        
        print(f"📋 Benchmark Configuration:")
        print(f"   Max test cases: {config.benchmark.max_test_cases}")
        print(f"   Iterations per test: {config.benchmark.benchmark_iterations}")
        print(f"   Speed benchmark: {config.benchmark.enable_speed_benchmark}")
        print(f"   Memory benchmark: {config.benchmark.enable_memory_benchmark}")
        print(f"   Accuracy benchmark: {config.benchmark.enable_accuracy_benchmark}")
        
        # Initialize systems
        print("\n🔧 Initializing systems...")
        inference_system = TJAInferenceSystem(config)
        benchmark = PerformanceBenchmark(config.benchmark, inference_system)
        
        # Run benchmark
        print("\n🚀 Running comprehensive benchmark...")
        results = benchmark.run_comprehensive_benchmark()
        
        # Save results
        output_dir = Path(args.output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        results_file = output_dir / f"benchmark_results_{int(time.time())}.json"
        benchmark.save_benchmark_results(results, str(results_file))
        
        # Print summary
        print(f"\n📊 Benchmark Results:")
        print(f"   Overall score: {results['overall_score']:.3f}")
        print(f"   Overall passed: {'✅' if results['overall_passed'] else '❌'}")
        print(f"   Total tests: {results['total_tests']}")
        print(f"   Passed tests: {results['passed_tests']}")
        print(f"   Failed tests: {results['failed_tests']}")
        print(f"   Execution time: {results['execution_time']:.2f}s")
        
        # Category breakdown
        for category, category_results in results["categories"].items():
            score = category_results.get("average_score", 0.0)
            passed = category_results.get("passed", False)
            status = "✅" if passed else "❌"
            print(f"   {category.capitalize()}: {score:.3f} {status}")
        
        print(f"\n📄 Results saved: {results_file}")
        
        return results
        
    except Exception as e:
        print(f"❌ Benchmark failed: {e}")
        import traceback
        traceback.print_exc()
        return {"success": False, "error": str(e)}


def run_validation_test(args) -> Dict[str, Any]:
    """Run validation framework test"""
    print("🔍 Phase 6 Validation Framework Test")
    print("=" * 40)
    
    try:
        # Create configuration
        config = create_phase6_config(
            experiment_name="validation_test",
            enable_validation=True,
            debug_mode=args.debug
        )
        
        print("🔧 Testing validation framework...")
        
        # Initialize validator
        validator = TJAValidator(config.get_validation_config())
        
        # Create test chart
        from src.phase6.tja_postprocessing import TJAChart, TJANote
        
        test_notes = []
        for i in range(20):
            note = TJANote(
                time=i * 0.5,
                note_type=1 if i % 2 == 0 else 2,  # Alternating don/ka
                position=i * 2,
                measure=i // 4,
                confidence=0.8 + np.random.normal(0, 0.1)
            )
            test_notes.append(note)
        
        test_chart = TJAChart(
            title="Test Chart",
            artist="AI Generated",
            bpm=140.0,
            offset=0.0,
            difficulty_level=9,
            course_type="oni",
            notes=test_notes,
            metadata={"test": True},
            measures=["1212,", "1212,", "1212,", "1212,", "1212,"]
        )
        
        # Run validation
        validation_results = validator.validate_generated_chart(
            test_chart, 
            {"bpm": 140.0, "difficulty_level": 9}
        )
        
        # Print results
        print(f"\n📊 Validation Results:")
        print(f"   Overall score: {validation_results['overall_score']:.3f}")
        print(f"   Overall passed: {'✅' if validation_results['overall_passed'] else '❌'}")
        print(f"   Validation time: {validation_results['validation_time']:.3f}s")
        
        # Component breakdown
        for component, results in validation_results["component_results"].items():
            score = results.get("score", 0.0)
            passed = results.get("passed", False)
            status = "✅" if passed else "❌"
            print(f"   {component.replace('_', ' ').title()}: {score:.3f} {status}")
        
        # Issues and recommendations
        summary = validation_results["summary"]
        if summary["critical_issues"]:
            print(f"\n⚠️  Critical Issues:")
            for issue in summary["critical_issues"]:
                print(f"     • {issue}")
        
        if summary["improvement_suggestions"]:
            print(f"\n💡 Improvement Suggestions:")
            for suggestion in summary["improvement_suggestions"]:
                print(f"     • {suggestion}")
        
        # Save results
        output_dir = Path(args.output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        results_file = output_dir / "validation_test_results.json"
        with open(results_file, 'w') as f:
            json.dump(validation_results, f, indent=2, default=str)
        
        print(f"\n📄 Results saved: {results_file}")
        
        return validation_results
        
    except Exception as e:
        print(f"❌ Validation test failed: {e}")
        import traceback
        traceback.print_exc()
        return {"success": False, "error": str(e)}


def main():
    """Main CLI interface"""
    parser = argparse.ArgumentParser(
        description="Phase 6: Inference Pipeline and Validation",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # System validation
  python main_phase6.py validate
  
  # Run inference on audio file
  python main_phase6.py inference audio.wav --bpm 140 --difficulty 9
  
  # Run performance benchmark
  python main_phase6.py benchmark --max-test-cases 10
  
  # Test validation framework
  python main_phase6.py test-validation
        """
    )
    
    # Global arguments
    parser.add_argument("--debug", action="store_true", help="Enable debug mode")
    parser.add_argument("--log-level", default="INFO", choices=["DEBUG", "INFO", "WARNING", "ERROR"])
    parser.add_argument("--output-dir", default="outputs/phase6", help="Output directory")
    
    # Subcommands
    subparsers = parser.add_subparsers(dest="command", help="Available commands")
    
    # Validate command
    validate_parser = subparsers.add_parser("validate", help="Validate system and environment")
    
    # Inference command
    inference_parser = subparsers.add_parser("inference", help="Run TJA inference")
    inference_parser.add_argument("audio_file", help="Path to audio file")
    inference_parser.add_argument("--bpm", type=float, required=True, help="Beats per minute")
    inference_parser.add_argument("--offset", type=float, default=0.0, help="Chart offset in seconds")
    inference_parser.add_argument("--difficulty", type=int, choices=[8, 9, 10], default=9, help="Difficulty level")
    inference_parser.add_argument("--course-type", choices=["oni", "edit"], default="oni", help="Course type")
    inference_parser.add_argument("--model-path", help="Path to trained model")
    inference_parser.add_argument("--experiment-name", default="phase6_inference", help="Experiment name")
    
    # Benchmark command
    benchmark_parser = subparsers.add_parser("benchmark", help="Run performance benchmark")
    benchmark_parser.add_argument("--max-test-cases", type=int, default=5, help="Maximum test cases")
    benchmark_parser.add_argument("--iterations", type=int, default=3, help="Iterations per test")
    
    # Test validation command
    test_validation_parser = subparsers.add_parser("test-validation", help="Test validation framework")
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logging(args.log_level)
    
    # Memory monitoring
    memory_monitor = MemoryMonitor()
    memory_monitor.log_memory_status("Phase 6 start")
    
    start_time = time.time()
    
    try:
        if args.command == "validate":
            result = validate_system()
            success = result.get("valid", False)
            
        elif args.command == "inference":
            result = run_inference(args)
            success = result.get("success", False)
            
        elif args.command == "benchmark":
            result = run_benchmark(args)
            success = result.get("overall_passed", False)
            
        elif args.command == "test-validation":
            result = run_validation_test(args)
            success = result.get("overall_passed", False)
            
        else:
            parser.print_help()
            success = False
        
        execution_time = time.time() - start_time
        
        print(f"\n⏱️  Total execution time: {execution_time:.2f} seconds")
        
        # Final memory status
        memory_monitor.log_memory_status("Phase 6 end")

        # Cleanup global resources
        print("\n🧹 Cleaning up resources...")
        cleanup_global_resources()

        if success:
            print("✅ Phase 6 operation completed successfully!")
            sys.exit(0)
        else:
            print("❌ Phase 6 operation failed!")
            sys.exit(1)

    except KeyboardInterrupt:
        print("\n⚠️  Operation cancelled by user")
        cleanup_global_resources()
        sys.exit(1)
    except MemoryError as e:
        print(f"\n💾 Out of memory: {e}")
        print("💡 Try:")
        print("   • Use a shorter audio file")
        print("   • Close other applications")
        print("   • Restart the system to free memory")
        cleanup_global_resources()
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        cleanup_global_resources()
        sys.exit(1)


if __name__ == "__main__":
    main()
