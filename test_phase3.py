#!/usr/bin/env python3
"""
Test Script for Phase 3 Implementation
Validates deep learning model functionality before full training
"""

import sys
import time
import torch
import numpy as np
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.model.tja_generator import TJ<PERSON>eneratorModel
from src.model.audio_encoder import AudioFeatureEncoder
from src.model.sequence_decoder import SequenceDecoder
from src.model.attention_modules import CrossModalAttention, TemporalAttention
from src.model.pattern_library import PatternLibrary
from src.model.loss_functions import TJAGenerationLoss
from src.model import PHASE_3_MODEL_CONFIG
from src.training.data_loader import TJAD<PERSON>set, TJADataLoader, collate_fn
from src.training.optimizer import create_optimizer, create_scheduler, estimate_memory_usage
from src.training.metrics import TJAMetrics, GenerationEvaluator


def test_audio_encoder():
    """Test audio feature encoder"""
    print("🎵 Testing Audio Feature Encoder...")
    
    try:
        config = PHASE_3_MODEL_CONFIG["architecture"]
        encoder = AudioFeatureEncoder(config)
        
        # Test input (memory optimized)
        batch_size = 2
        seq_len = 250  # Reduced to match memory-optimized config
        feature_dim = 201

        audio_features = torch.randn(batch_size, seq_len, feature_dim)
        attention_mask = torch.ones(batch_size, seq_len)
        
        # Forward pass
        outputs = encoder(audio_features, attention_mask)
        
        print(f"  ✅ Input shape: {audio_features.shape}")
        print(f"  ✅ Output shape: {outputs['encoded_features'].shape}")
        print(f"  ✅ Expected output dims: [batch, time, {config['hidden_dims']}]")
        
        # Validate output shape
        expected_shape = (batch_size, seq_len, config["hidden_dims"])
        actual_shape = outputs["encoded_features"].shape
        
        if actual_shape == expected_shape:
            print(f"  ✅ Audio encoder test PASSED")
            return True
        else:
            print(f"  ❌ Shape mismatch: expected {expected_shape}, got {actual_shape}")
            return False
            
    except Exception as e:
        print(f"  ❌ Audio encoder test FAILED: {e}")
        return False


def test_sequence_decoder():
    """Test sequence decoder"""
    print("🎼 Testing Sequence Decoder...")
    
    try:
        config = PHASE_3_MODEL_CONFIG["architecture"]
        decoder = SequenceDecoder(config)
        
        # Test input (memory optimized)
        batch_size = 2
        audio_len = 250  # Reduced to match memory-optimized config
        seq_len = 100
        hidden_dims = config["hidden_dims"]
        
        audio_features = torch.randn(batch_size, audio_len, hidden_dims)
        target_sequence = torch.randint(0, 8, (batch_size, seq_len))
        target_timing = torch.randint(0, 16, (batch_size, seq_len))
        difficulty = torch.randint(0, 3, (batch_size,))
        
        # Training forward pass
        outputs = decoder(
            audio_features=audio_features,
            target_sequence=target_sequence,
            target_timing=target_timing,
            difficulty=difficulty
        )
        
        print(f"  ✅ Training mode:")
        print(f"    Input audio: {audio_features.shape}")
        print(f"    Target sequence: {target_sequence.shape}")
        print(f"    Output logits: {outputs['logits'].shape}")
        
        # Inference forward pass
        inference_outputs = decoder(
            audio_features=audio_features,
            difficulty=difficulty
        )
        
        print(f"  ✅ Inference mode:")
        print(f"    Generated sequence: {inference_outputs['generated_sequence'].shape}")
        
        # Validate shapes
        vocab_size = config.get("note_types", 8)
        expected_logits_shape = (batch_size, seq_len, vocab_size)
        if outputs["logits"].shape == expected_logits_shape:
            print(f"  ✅ Sequence decoder test PASSED")
            return True
        else:
            print(f"  ❌ Logits shape mismatch: expected {expected_logits_shape}, got {outputs['logits'].shape}")
            return False
            
    except Exception as e:
        print(f"  ❌ Sequence decoder test FAILED: {e}")
        return False


def test_attention_modules():
    """Test attention modules"""
    print("🔍 Testing Attention Modules...")
    
    try:
        d_model = 256
        num_heads = 8
        batch_size = 2
        seq_len = 100
        
        # Test CrossModalAttention
        cross_attention = CrossModalAttention(d_model, num_heads)
        
        audio_features = torch.randn(batch_size, seq_len, d_model)
        rhythm_features = torch.randn(batch_size, seq_len // 2, d_model)
        
        cross_outputs = cross_attention(audio_features, rhythm_features)
        
        print(f"  ✅ CrossModalAttention:")
        print(f"    Fused features: {cross_outputs['fused_features'].shape}")
        
        # Test TemporalAttention
        temporal_attention = TemporalAttention(d_model, num_heads)
        
        features = torch.randn(batch_size, seq_len, d_model)
        beat_positions = torch.randint(0, 4, (batch_size, seq_len))
        
        temporal_outputs = temporal_attention(
            features, beat_positions=beat_positions
        )
        
        print(f"  ✅ TemporalAttention:")
        print(f"    Attended features: {temporal_outputs['attended_features'].shape}")
        
        print(f"  ✅ Attention modules test PASSED")
        return True
        
    except Exception as e:
        print(f"  ❌ Attention modules test FAILED: {e}")
        return False


def test_pattern_library():
    """Test pattern library"""
    print("📚 Testing Pattern Library...")

    try:
        config = PHASE_3_MODEL_CONFIG["architecture"]
        pattern_library = PatternLibrary(config)
        
        # Test pattern encoding
        batch_size = 2
        pattern_length = 8
        
        note_sequence = torch.randint(0, 8, (batch_size, pattern_length))
        timing_sequence = torch.randint(0, 16, (batch_size, pattern_length))
        difficulty = torch.randint(0, 3, (batch_size,))
        
        pattern_embeddings = pattern_library.encode_sequence_patterns(
            note_sequence, timing_sequence, difficulty, pattern_length
        )
        
        print(f"  ✅ Pattern encoding:")
        print(f"    Input sequence: {note_sequence.shape}")
        print(f"    Pattern embeddings: {pattern_embeddings.shape}")
        
        # Test pattern retrieval
        embedding_dim = config.get("pattern_context_dims", 16)  # Use correct dimension
        query_embedding = torch.randn(batch_size, embedding_dim)
        similar_patterns, similarities = pattern_library.retrieve_similar_patterns(
            query_embedding, top_k=5
        )
        
        print(f"  ✅ Pattern retrieval:")
        print(f"    Similar patterns: {similar_patterns.shape}")
        print(f"    Similarities: {similarities.shape}")
        
        print(f"  ✅ Pattern library test PASSED")
        return True
        
    except Exception as e:
        print(f"  ❌ Pattern library test FAILED: {e}")
        return False


def test_loss_functions():
    """Test loss functions"""
    print("📊 Testing Loss Functions...")
    
    try:
        config = PHASE_3_MODEL_CONFIG["architecture"]
        loss_fn = TJAGenerationLoss(config)
        
        # Test data
        batch_size = 2
        seq_len = 100
        vocab_size = config.get("note_types", 8)
        
        # Predictions
        logits = torch.randn(batch_size, seq_len, vocab_size)
        hidden_states = torch.randn(batch_size, seq_len, config["hidden_dims"])
        
        predictions = {
            "logits": logits,
            "hidden_states": hidden_states
        }
        
        # Targets
        note_sequence = torch.randint(0, vocab_size, (batch_size, seq_len))
        timing_sequence = torch.randint(0, 16, (batch_size, seq_len))
        difficulty = torch.randint(0, 3, (batch_size,))
        attention_mask = torch.ones(batch_size, seq_len)
        
        targets = {
            "note_sequence": note_sequence,
            "timing_sequence": timing_sequence,
            "difficulty": difficulty,
            "attention_mask": attention_mask
        }
        
        # Compute loss
        loss_outputs = loss_fn(predictions, targets)
        
        print(f"  ✅ Loss computation:")
        print(f"    Total loss: {loss_outputs['total_loss'].item():.4f}")
        print(f"    Note loss: {loss_outputs['note_loss'].item():.4f}")
        print(f"    Timing loss: {loss_outputs['timing_loss'].item():.4f}")
        print(f"    Difficulty loss: {loss_outputs['difficulty_loss'].item():.4f}")
        
        # Test accuracy metrics
        accuracy_metrics = loss_fn.compute_accuracy_metrics(predictions, targets)
        
        print(f"  ✅ Accuracy metrics:")
        print(f"    Note accuracy: {accuracy_metrics['note_accuracy']:.3f}")
        print(f"    Sequence accuracy: {accuracy_metrics['sequence_accuracy']:.3f}")
        
        print(f"  ✅ Loss functions test PASSED")
        return True
        
    except Exception as e:
        print(f"  ❌ Loss functions test FAILED: {e}")
        return False


def test_complete_model():
    """Test complete TJA generator model"""
    print("🤖 Testing Complete TJA Generator Model...")
    
    try:
        config = PHASE_3_MODEL_CONFIG["architecture"]
        model = TJAGeneratorModel(config)
        
        # Test data (memory optimized)
        batch_size = 2
        audio_len = 250  # Reduced to match memory-optimized config
        seq_len = 100
        
        audio_features = torch.randn(batch_size, audio_len, 201)  # Phase 2 features
        target_sequence = torch.randint(0, 8, (batch_size, seq_len))
        target_timing = torch.randint(0, 16, (batch_size, seq_len))
        difficulty = torch.randint(0, 3, (batch_size,))
        audio_mask = torch.ones(batch_size, audio_len)
        
        # Training forward pass
        outputs = model(
            audio_features=audio_features,
            target_sequence=target_sequence,
            target_timing=target_timing,
            difficulty=difficulty,
            audio_mask=audio_mask,
            return_loss=True
        )
        
        print(f"  ✅ Training mode:")
        print(f"    Input audio features: {audio_features.shape}")
        print(f"    Output logits: {outputs['logits'].shape}")
        print(f"    Total loss: {outputs['total_loss'].item():.4f}")
        
        # Generation test
        generated = model.generate(
            audio_features=audio_features,
            difficulty=1,  # Oni 9
            temperature=1.0
        )
        
        print(f"  ✅ Generation mode:")
        if "generated_sequence" in generated:
            print(f"    Generated sequence: {generated['generated_sequence'].shape}")
        
        # Model size info
        model_info = model.get_model_size()
        print(f"  ✅ Model info:")
        print(f"    Total parameters: {model_info['total_parameters']:,}")
        print(f"    Model size: {model_info['model_size_mb']:.1f} MB")
        
        print(f"  ✅ Complete model test PASSED")
        return True
        
    except Exception as e:
        print(f"  ❌ Complete model test FAILED: {e}")
        return False


def test_data_loading():
    """Test data loading pipeline"""
    print("📁 Testing Data Loading Pipeline...")
    
    try:
        # Check if Phase 2 outputs exist
        catalog_path = "data/processed/audio_features/phase3_input_catalog.json"
        phase1_catalog_path = "data/processed/catalog.json"
        
        if not Path(catalog_path).exists():
            print(f"  ⚠️  Phase 3 catalog not found: {catalog_path}")
            print(f"  ⚠️  Skipping data loading test (run Phase 2 first)")
            return True
        
        if not Path(phase1_catalog_path).exists():
            print(f"  ⚠️  Phase 1 catalog not found: {phase1_catalog_path}")
            print(f"  ⚠️  Skipping data loading test (run Phase 1 first)")
            return True
        
        # Test dataset creation
        dataset = TJADataset(
            catalog_path=catalog_path,
            phase1_catalog_path=phase1_catalog_path,
            max_audio_length=500,
            max_sequence_length=100
        )
        
        print(f"  ✅ Dataset created with {len(dataset)} samples")
        
        if len(dataset) > 0:
            # Test sample loading
            sample = dataset[0]
            
            print(f"  ✅ Sample structure:")
            for key, value in sample.items():
                if isinstance(value, torch.Tensor):
                    print(f"    {key}: {value.shape} ({value.dtype})")
                else:
                    print(f"    {key}: {type(value)} - {value}")
            
            # Test data loader
            from torch.utils.data import DataLoader
            
            data_loader = DataLoader(
                dataset, batch_size=2, shuffle=False, collate_fn=collate_fn
            )
            
            batch = next(iter(data_loader))
            
            print(f"  ✅ Batch structure:")
            for key, value in batch.items():
                if isinstance(value, torch.Tensor):
                    print(f"    {key}: {value.shape} ({value.dtype})")
                elif isinstance(value, list):
                    print(f"    {key}: list of {len(value)} items")
        
        print(f"  ✅ Data loading test PASSED")
        return True
        
    except Exception as e:
        print(f"  ❌ Data loading test FAILED: {e}")
        return False


def test_memory_estimation():
    """Test memory usage estimation"""
    print("💾 Testing Memory Usage Estimation...")
    
    try:
        config = PHASE_3_MODEL_CONFIG["architecture"]
        model = TJAGeneratorModel(config)
        
        batch_size = 4
        sequence_length = 1000
        
        memory_est = estimate_memory_usage(model, batch_size, sequence_length)
        
        print(f"  ✅ Memory estimates for batch_size={batch_size}, seq_len={sequence_length}:")
        for component, memory_gb in memory_est.items():
            print(f"    {component}: {memory_gb:.2f} GB")
        
        # Check RTX 3070 compatibility (8GB VRAM)
        total_memory = memory_est["safety_margin"]
        if total_memory <= 8.0:
            print(f"  ✅ Memory usage ({total_memory:.2f} GB) fits in RTX 3070 (8GB)")
        else:
            print(f"  ⚠️  Memory usage ({total_memory:.2f} GB) may exceed RTX 3070 capacity")
        
        print(f"  ✅ Memory estimation test PASSED")
        return True
        
    except Exception as e:
        print(f"  ❌ Memory estimation test FAILED: {e}")
        return False


def test_metrics():
    """Test metrics and evaluation"""
    print("📈 Testing Metrics and Evaluation...")
    
    try:
        # Test TJAMetrics
        metrics = TJAMetrics()
        
        # Simulate some outputs and batch
        batch_size = 2
        seq_len = 100
        vocab_size = 8
        
        outputs = {
            "total_loss": torch.tensor(1.5),
            "note_loss": torch.tensor(1.2),
            "logits": torch.randn(batch_size, seq_len, vocab_size),
            "accuracy_metrics": {
                "note_accuracy": 0.75,
                "sequence_accuracy": 0.25
            }
        }
        
        batch = {
            "note_sequence": torch.randint(0, vocab_size, (batch_size, seq_len)),
            "sequence_mask": torch.ones(batch_size, seq_len)
        }
        
        # Update metrics
        metrics.update(outputs, batch)
        computed_metrics = metrics.compute()
        
        print(f"  ✅ Metrics computation:")
        for key, value in computed_metrics.items():
            print(f"    {key}: {value:.4f}")
        
        # Test GenerationEvaluator
        config = {"generation_samples": 2, "temperature": 1.0}
        evaluator = GenerationEvaluator(config)
        
        # Test sample evaluation
        generated_sequence = torch.randint(0, 8, (50,))
        target_sequence = torch.randint(0, 8, (50,))
        difficulty = torch.tensor(1)
        
        sample_result = evaluator._evaluate_sample(
            generated_sequence, target_sequence, difficulty, "test_song"
        )
        
        print(f"  ✅ Sample evaluation:")
        print(f"    Token accuracy: {sample_result['metrics']['token_accuracy']:.3f}")
        print(f"    Difficulty appropriateness: {sample_result['metrics']['difficulty_appropriateness']:.3f}")
        
        print(f"  ✅ Metrics test PASSED")
        return True
        
    except Exception as e:
        print(f"  ❌ Metrics test FAILED: {e}")
        return False


def main():
    """Run all Phase 3 tests"""
    print("🧪 TJA Generator Phase 3 - Test Suite")
    print("=" * 50)
    
    tests = [
        ("Audio Feature Encoder", test_audio_encoder),
        ("Sequence Decoder", test_sequence_decoder),
        ("Attention Modules", test_attention_modules),
        ("Pattern Library", test_pattern_library),
        ("Loss Functions", test_loss_functions),
        ("Complete Model", test_complete_model),
        ("Data Loading Pipeline", test_data_loading),
        ("Memory Estimation", test_memory_estimation),
        ("Metrics and Evaluation", test_metrics)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        try:
            if test_func():
                passed += 1
                print(f"  ✅ {test_name} PASSED")
            else:
                print(f"  ❌ {test_name} FAILED")
        except Exception as e:
            print(f"  💥 {test_name} CRASHED: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Phase 3 implementation is ready for training.")
        return 0
    else:
        print("⚠️  Some tests failed. Please review the implementation.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
