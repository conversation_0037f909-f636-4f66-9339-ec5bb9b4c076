#!/usr/bin/env python3
"""
Simple Phase 4 Test

Direct test of the TJA generation pipeline with synthetic features
"""

import sys
import torch
import numpy as np
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.phase4.pipeline import TJAGenerationPipeline
from src.phase4.config import PHASE_4_CONFIG

def test_direct_generation():
    """Test direct TJA generation with synthetic features"""
    print("🧪 Testing Direct TJA Generation...")
    
    # Initialize pipeline
    pipeline = TJAGenerationPipeline()
    
    if not pipeline.initialize():
        print("❌ Pipeline initialization failed")
        return False
    
    print("✅ Pipeline initialized successfully")
    
    # Create synthetic audio features
    seq_len = 200
    feature_dim = 201
    audio_features = torch.randn(seq_len, feature_dim)
    
    print(f"📊 Created synthetic features: {audio_features.shape}")
    
    # Test sequence generation directly
    try:
        device = next(pipeline.model.parameters()).device
        audio_features_batch = audio_features.unsqueeze(0).to(device)  # Add batch dimension
        
        print("🎵 Generating TJA sequences...")
        
        sequences = {}
        for difficulty in [8, 9, 10]:
            model_difficulty = difficulty - 8
            
            with torch.no_grad():
                outputs = pipeline.model(
                    audio_features=audio_features_batch,
                    difficulty=torch.tensor([model_difficulty]).to(device),
                    return_loss=False
                )
                
                generated_sequence = torch.argmax(outputs['logits'], dim=-1)
                sequences[difficulty] = generated_sequence[0].cpu()
                
                print(f"  ✅ Difficulty {difficulty}: {generated_sequence.shape}")
        
        # Create TJA content
        print("📝 Creating TJA file content...")
        
        audio_info = {
            "file_name": "test_audio.wav",
            "file_size_mb": 1.0,
            "format": ".wav"
        }
        
        metadata = {
            "title": "Test Song",
            "bpm": 120.0,
            "artist": "Test Artist"
        }
        
        tja_content = pipeline._create_tja_content(sequences, audio_info, metadata)
        
        # Save TJA file
        output_path = Path("test_direct_output.tja")
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(tja_content)
        
        print(f"✅ TJA file saved: {output_path}")
        
        # Validate TJA content
        print("🔍 Validating TJA content...")
        
        with open(output_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check required elements
        required_elements = [
            "TITLE:Test Song",
            "BPM:120.0",
            "WAVE:test_audio.wav",
            "#START",
            "#END",
            "COURSE:Oni"
        ]
        
        for element in required_elements:
            if element in content:
                print(f"  ✅ Found: {element}")
            else:
                print(f"  ❌ Missing: {element}")
                return False
        
        # Check sequence content
        lines = content.split('\n')
        sequence_lines = [line for line in lines if line and not line.startswith('#') and not line.startswith('TITLE') and not line.startswith('BPM') and ':' not in line]
        
        if sequence_lines:
            print(f"  ✅ Found {len(sequence_lines)} sequence lines")
            print(f"  📝 Sample sequence: {sequence_lines[0][:20]}...")
        else:
            print("  ❌ No sequence lines found")
            return False
        
        print("🎉 Direct TJA generation test PASSED!")
        
        # Cleanup
        pipeline.cleanup()
        
        return True
        
    except Exception as e:
        print(f"❌ Direct generation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_quality_assessment():
    """Test quality assessment functionality"""
    print("\n🔍 Testing Quality Assessment...")
    
    try:
        from src.phase4.quality_assessment import QualityAssessment
        
        qa = QualityAssessment(PHASE_4_CONFIG["quality_assessment"])
        
        # Create test sequence
        test_sequence = np.array([0, 1, 0, 2, 0, 1, 2, 0] * 20)  # 160 notes
        
        # Evaluate sequence
        metrics = qa.evaluate_sequence(test_sequence, difficulty_level=9)
        
        print(f"  📊 Quality Metrics:")
        print(f"    Overall Score: {metrics.overall_score:.3f}")
        print(f"    Musical Accuracy: {metrics.musical_accuracy:.3f}")
        print(f"    Difficulty Appropriateness: {metrics.difficulty_appropriateness:.3f}")
        print(f"    Pattern Coherence: {metrics.pattern_coherence:.3f}")
        print(f"    Rhythmic Consistency: {metrics.rhythmic_consistency:.3f}")
        print(f"    Playability Score: {metrics.playability_score:.3f}")
        print(f"    Timing Precision: {metrics.timing_precision:.3f}")
        
        # Check note density
        density = metrics.note_density_analysis
        print(f"    Note Density: {density['overall_density']:.3f}")
        print(f"    Total Notes: {density['total_notes']}")
        
        print("✅ Quality assessment test PASSED!")
        return True
        
    except Exception as e:
        print(f"❌ Quality assessment test failed: {e}")
        return False

def main():
    """Run simple Phase 4 tests"""
    print("🚀 Phase 4 Simple Test Suite")
    print("=" * 50)
    
    tests = [
        ("Direct TJA Generation", test_direct_generation),
        ("Quality Assessment", test_quality_assessment)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * 30)
        
        if test_func():
            passed += 1
        else:
            print(f"❌ {test_name} FAILED")
    
    print("\n" + "=" * 50)
    print(f"📊 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests PASSED!")
        return 0
    else:
        print("❌ Some tests FAILED")
        return 1

if __name__ == "__main__":
    sys.exit(main())
