#!/usr/bin/env python3
"""
Optimized Phase 5 Training Session

Execute a sample training session with optimized configuration based on
performance analysis results, demonstrating curriculum learning and
adaptive loss weighting in action.
"""

import sys
import time
import torch
import json
from pathlib import Path
from unittest.mock import Mock

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.phase5 import (
    Phase5TrainingConfig, OptimizationConfig, AdvancedTJATrainer,
    create_phase5_config
)
from src.model.tja_generator import TJAGeneratorModel
from src.utils.memory_monitor import MemoryMonitor


def create_optimized_config() -> Phase5TrainingConfig:
    """Create optimized configuration based on performance analysis"""
    # Load optimization results
    try:
        with open("outputs/phase5_optimization/performance_optimization_results.json", 'r') as f:
            optimization_results = json.load(f)
        
        optimal_batch_size = optimization_results["resource_optimization"]["optimal_batch_size"]
        optimal_grad_accum = optimization_results["resource_optimization"]["optimal_gradient_accumulation"]
        
        print(f"📊 Using optimized configuration:")
        print(f"   Batch size: {optimal_batch_size}")
        print(f"   Gradient accumulation: {optimal_grad_accum}")
        print(f"   Effective batch size: {optimal_batch_size * optimal_grad_accum}")
        
    except FileNotFoundError:
        print("⚠️ Optimization results not found, using default configuration")
        optimal_batch_size = 4
        optimal_grad_accum = 4
    
    # Create optimized configuration
    config = Phase5TrainingConfig(
        experiment_name="optimized_phase5_demo",
        batch_size=optimal_batch_size,
        gradient_accumulation_steps=optimal_grad_accum,
        learning_rate=1e-4,
        weight_decay=0.01,
        
        # Optimization features
        mixed_precision=True,
        gradient_checkpointing=True,
        compile_model=False,  # Disabled due to Triton dependency
        use_fused_optimizer=False,  # Disabled for compatibility
        
        # Training parameters (shortened for demo)
        total_training_steps=1000,  # Short demo training
        validation_frequency=100,
        checkpoint_frequency=200,
        early_stopping_patience=5,
        
        # Advanced features
        use_curriculum_learning=True,
        use_data_augmentation=True,
        use_adaptive_loss_weighting=True,
        
        # Logging
        log_frequency=50,
        wandb_project=None  # Disable wandb for demo
    )
    
    return config


def create_mock_data_loaders(config: Phase5TrainingConfig):
    """Create mock data loaders for demonstration"""
    from torch.utils.data import DataLoader, TensorDataset
    
    # Create synthetic training data
    batch_size = config.batch_size
    num_samples = 100  # Small dataset for demo
    seq_len = 200  # Shorter sequences for faster training
    feature_dim = 201
    
    # Generate synthetic data
    audio_features = torch.randn(num_samples, seq_len, feature_dim)
    note_sequences = torch.randint(0, 8, (num_samples, seq_len))
    timing_sequences = torch.randint(0, 64, (num_samples, seq_len))
    difficulties = torch.randint(0, 3, (num_samples,))
    song_ids = [f"demo_song_{i}" for i in range(num_samples)]
    
    # Create dataset
    class MockTJADataset:
        def __init__(self, audio_features, note_sequences, timing_sequences, difficulties, song_ids):
            self.audio_features = audio_features
            self.note_sequences = note_sequences
            self.timing_sequences = timing_sequences
            self.difficulties = difficulties
            self.song_ids = song_ids
        
        def __len__(self):
            return len(self.audio_features)
        
        def __getitem__(self, idx):
            return {
                "audio_features": self.audio_features[idx],
                "note_sequence": self.note_sequences[idx],
                "timing_sequence": self.timing_sequences[idx],
                "difficulty": self.difficulties[idx],
                "song_ids": self.song_ids[idx]
            }
    
    # Create datasets
    train_size = int(0.8 * num_samples)
    val_size = num_samples - train_size
    
    train_dataset = MockTJADataset(
        audio_features[:train_size],
        note_sequences[:train_size],
        timing_sequences[:train_size],
        difficulties[:train_size],
        song_ids[:train_size]
    )
    
    val_dataset = MockTJADataset(
        audio_features[train_size:],
        note_sequences[train_size:],
        timing_sequences[train_size:],
        difficulties[train_size:],
        song_ids[train_size:]
    )
    
    # Create data loaders
    train_loader = DataLoader(
        train_dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=0,  # No multiprocessing for demo
        pin_memory=True
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=0,
        pin_memory=True
    )
    
    return train_loader, val_loader


def run_optimized_training_session():
    """Run optimized training session demonstration"""
    print("🚀 Phase 5 Optimized Training Session")
    print("=" * 50)
    
    # Memory monitoring
    memory_monitor = MemoryMonitor()
    memory_monitor.log_memory_status("Training session start")
    
    start_time = time.time()
    
    try:
        # Create optimized configuration
        config = create_optimized_config()
        
        # Create optimization configuration
        optimization_config = OptimizationConfig(
            use_hyperparameter_optimization=False,  # Disabled for demo
            curriculum_stages=[
                {
                    "name": "basic_patterns",
                    "steps": 300,
                    "difficulty_focus": [0],
                    "max_sequence_length": 150,
                    "loss_weights": {"note_type": 1.0, "timing": 0.5, "pattern": 0.3}
                },
                {
                    "name": "intermediate_patterns",
                    "steps": 400,
                    "difficulty_focus": [0, 1],
                    "max_sequence_length": 175,
                    "loss_weights": {"note_type": 1.0, "timing": 0.7, "pattern": 0.5}
                },
                {
                    "name": "advanced_patterns",
                    "steps": 300,
                    "difficulty_focus": [0, 1, 2],
                    "max_sequence_length": 200,
                    "loss_weights": {"note_type": 1.0, "timing": 0.8, "pattern": 0.6}
                }
            ]
        )
        
        # Create data loaders
        print("\n📊 Creating mock data loaders...")
        train_loader, val_loader = create_mock_data_loaders(config)
        print(f"   Train batches: {len(train_loader)}")
        print(f"   Validation batches: {len(val_loader)}")
        
        # Create model
        print("\n🧠 Creating TJA generation model...")
        model = TJAGeneratorModel(config.get_model_config())
        param_count = sum(p.numel() for p in model.parameters())
        print(f"   Parameters: {param_count:,}")
        print(f"   Model size: {param_count * 4 / (1024*1024):.1f}MB")
        
        # Create trainer
        print("\n🎯 Initializing advanced trainer...")
        trainer = AdvancedTJATrainer(
            model=model,
            config=config,
            optimization_config=optimization_config,
            train_loader=train_loader,
            val_loader=val_loader,
            output_dir="outputs/phase5_optimized_training"
        )
        
        # Run training
        print("\n🚀 Starting optimized training session...")
        print("   Features enabled:")
        print("     ✅ Curriculum learning (3 stages)")
        print("     ✅ Adaptive loss weighting")
        print("     ✅ Advanced data augmentation")
        print("     ✅ Mixed precision training")
        print("     ✅ Gradient checkpointing")
        print("     ✅ Real-time diagnostics")
        
        training_results = trainer.train()
        
        # Training results
        training_time = time.time() - start_time
        
        print("\n📊 Training Session Results:")
        print("=" * 40)
        
        if training_results["success"]:
            print("✅ Training completed successfully!")
            print(f"   Total steps: {training_results['total_steps']}")
            print(f"   Total epochs: {training_results['total_epochs']}")
            print(f"   Training time: {training_results['total_training_time']:.2f}s")
            print(f"   Best validation loss: {training_results['best_val_loss']:.4f}")
            print(f"   Best quality score: {training_results['best_quality_score']:.4f}")
            
            # Save final model
            final_model_path = Path("outputs/phase5_optimized_training/final_optimized_model.pt")
            trainer.save_final_model(str(final_model_path))
            print(f"   Final model saved: {final_model_path}")
            
            # Export for Phase 4 deployment
            deployment_path = Path("outputs/phase5_optimized_training/deployment_model.pt")
            trainer.checkpoint_manager.export_for_phase4_deployment(str(deployment_path))
            print(f"   Deployment model exported: {deployment_path}")
            
        else:
            print("❌ Training failed!")
            print(f"   Error: {training_results.get('error', 'Unknown error')}")
        
        # Memory status
        memory_monitor.log_memory_status("Training session end")
        
        # Final diagnostics
        if hasattr(trainer, 'diagnostics'):
            final_diagnostics = trainer.diagnostics.get_final_diagnostics()
            
            print(f"\n🔍 Final Diagnostics:")
            if "summary" in final_diagnostics:
                summary = final_diagnostics["summary"]
                print(f"   Loss improvement: {summary['training_progress']['loss_improvement']*100:.1f}%")
                print(f"   Average throughput: {summary['performance']['avg_throughput']:.1f} samples/sec")
                print(f"   Peak GPU memory: {summary['resource_usage']['peak_gpu_memory_gb']:.2f}GB")
                print(f"   Issues detected: {len(summary['issues_detected'])}")
        
        return training_results
        
    except Exception as e:
        print(f"❌ Training session failed: {e}")
        import traceback
        traceback.print_exc()
        return {"success": False, "error": str(e)}


def test_hyperparameter_optimization():
    """Test hyperparameter optimization functionality with limited trials"""
    print("\n🔧 Testing Hyperparameter Optimization")
    print("-" * 40)
    
    try:
        # Create base configuration
        base_config = create_phase5_config(experiment_name="hyperopt_test")
        
        # Create optimization configuration with limited trials
        optimization_config = OptimizationConfig(
            use_hyperparameter_optimization=True,
            optimization_trials=3,  # Very limited for demo
            optimization_timeout_hours=0.1  # 6 minutes max
        )
        
        # Create mock data loaders
        train_loader, val_loader = create_mock_data_loaders(base_config)
        
        print(f"🔍 Running limited hyperparameter optimization...")
        print(f"   Trials: {optimization_config.optimization_trials}")
        print(f"   Timeout: {optimization_config.optimization_timeout_hours * 60:.0f} minutes")
        
        # Import and run hyperparameter optimization
        from src.phase5.hyperparameter_optimizer import HyperparameterOptimizer
        
        optimizer = HyperparameterOptimizer(
            base_config=base_config,
            optimization_config=optimization_config,
            train_loader=train_loader,
            val_loader=val_loader,
            output_dir="outputs/phase5_hyperopt_test"
        )
        
        # Run optimization (this will be very limited)
        optimization_results = optimizer.optimize(n_trials=2, timeout=60)  # 1 minute max
        
        if optimization_results.get("success", True):
            print("✅ Hyperparameter optimization test completed!")
            if "best_params" in optimization_results:
                print(f"   Best parameters found: {optimization_results['best_params']}")
                print(f"   Best score: {optimization_results.get('best_score', 'N/A')}")
        else:
            print("⚠️ Hyperparameter optimization test had issues")
        
        return optimization_results
        
    except Exception as e:
        print(f"⚠️ Hyperparameter optimization test failed: {e}")
        return {"success": False, "error": str(e)}


def main():
    """Main execution function"""
    print("🎯 Phase 5 Optimized Training Workflow Demonstration")
    print("=" * 60)
    
    start_time = time.time()
    
    try:
        # Run optimized training session
        training_results = run_optimized_training_session()
        
        # Test hyperparameter optimization
        hyperopt_results = test_hyperparameter_optimization()
        
        # Final summary
        total_time = time.time() - start_time
        
        print("\n" + "=" * 60)
        print("🎉 Phase 5 Training Workflow Demonstration Complete!")
        print("=" * 60)
        
        print(f"⏱️  Total execution time: {total_time:.2f} seconds")
        print(f"🚀 Training session: {'✅ SUCCESS' if training_results.get('success') else '❌ FAILED'}")
        print(f"🔧 Hyperparameter optimization: {'✅ TESTED' if hyperopt_results.get('success', True) else '❌ FAILED'}")
        
        print(f"\n🎯 Key Achievements:")
        print(f"   ✅ Optimized configuration based on performance analysis")
        print(f"   ✅ Curriculum learning with 3-stage progression")
        print(f"   ✅ Adaptive loss weighting in action")
        print(f"   ✅ Advanced data augmentation applied")
        print(f"   ✅ Real-time training diagnostics")
        print(f"   ✅ Quality assessment integration")
        print(f"   ✅ Model export for Phase 4 deployment")
        
        return True
        
    except Exception as e:
        print(f"❌ Workflow demonstration failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
