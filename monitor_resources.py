#!/usr/bin/env python3
"""
Resource Monitoring Script

Real-time monitoring of system resources with alerts and recommendations
for optimal Phase 6 TJA Generator performance.
"""

import sys
import time
import argparse
import signal
from pathlib import Path
from typing import Dict, Any

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.utils.resource_manager import ResourceManager, ResourceLimits
import psutil
import torch


class ResourceMonitorApp:
    """Interactive resource monitoring application"""
    
    def __init__(self, update_interval: float = 2.0):
        self.update_interval = update_interval
        self.running = False
        
        # Initialize resource manager
        self.resource_manager = ResourceManager()
        
        # Setup signal handlers
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        print("\n🛑 Shutting down resource monitor...")
        self.running = False
    
    def detect_system_resources(self) -> Dict[str, Any]:
        """Detect and display system resources"""
        print("🔍 System Resource Detection")
        print("=" * 40)
        
        resources = {}
        
        try:
            # System memory
            memory = psutil.virtual_memory()
            memory_gb = memory.total / (1024**3)
            available_gb = memory.available / (1024**3)
            
            resources["system_memory"] = {
                "total_gb": memory_gb,
                "available_gb": available_gb,
                "used_percent": memory.percent
            }
            
            print(f"💾 System Memory:")
            print(f"   Total: {memory_gb:.1f}GB")
            print(f"   Available: {available_gb:.1f}GB")
            print(f"   Used: {memory.percent:.1f}%")
            
            # Memory recommendations
            if memory_gb < 8.0:
                print(f"   ⚠️  Limited system memory")
                print(f"   💡 Recommendations:")
                print(f"     • Close unnecessary applications")
                print(f"     • Use audio files < 3 minutes")
                print(f"     • Consider upgrading to 8GB+ RAM")
            elif memory_gb >= 16.0:
                print(f"   ✅ Excellent memory capacity")
            else:
                print(f"   ✅ Good memory capacity")
            
            # GPU detection
            if torch.cuda.is_available():
                gpu_props = torch.cuda.get_device_properties(0)
                gpu_memory_gb = gpu_props.total_memory / (1024**3)
                
                resources["gpu"] = {
                    "name": gpu_props.name,
                    "memory_gb": gpu_memory_gb,
                    "compute_capability": f"{gpu_props.major}.{gpu_props.minor}"
                }
                
                print(f"\n🎮 GPU:")
                print(f"   Name: {gpu_props.name}")
                print(f"   Memory: {gpu_memory_gb:.1f}GB")
                print(f"   Compute: {gpu_props.major}.{gpu_props.minor}")
                
                # GPU recommendations
                if "RTX 3070" in gpu_props.name:
                    print(f"   ✅ Optimal GPU for TJA Generator")
                elif gpu_memory_gb >= 6.0:
                    print(f"   ✅ Excellent GPU memory")
                elif gpu_memory_gb >= 4.0:
                    print(f"   ✅ Good GPU memory")
                else:
                    print(f"   ⚠️  Limited GPU memory - CPU fallback recommended")
            else:
                print(f"\n🎮 GPU: Not available")
                print(f"   💡 CPU-only inference will be used")
            
            # CPU information
            cpu_count = psutil.cpu_count()
            cpu_freq = psutil.cpu_freq()
            
            resources["cpu"] = {
                "cores": cpu_count,
                "frequency_mhz": cpu_freq.current if cpu_freq else 0
            }
            
            print(f"\n🖥️  CPU:")
            print(f"   Cores: {cpu_count}")
            if cpu_freq:
                print(f"   Frequency: {cpu_freq.current:.0f}MHz")
            
            # Storage information
            disk = psutil.disk_usage('.')
            disk_free_gb = disk.free / (1024**3)
            
            resources["storage"] = {
                "free_gb": disk_free_gb
            }
            
            print(f"\n💿 Storage:")
            print(f"   Free space: {disk_free_gb:.1f}GB")
            
            if disk_free_gb < 5.0:
                print(f"   ⚠️  Low disk space")
                print(f"   💡 Free up space for temporary files")
            
            return resources
            
        except Exception as e:
            print(f"❌ Resource detection failed: {e}")
            return {}
    
    def get_optimal_settings(self, resources: Dict[str, Any]) -> Dict[str, Any]:
        """Get optimal Phase 6 settings based on detected resources"""
        print(f"\n⚙️  Optimal Settings Recommendations")
        print("=" * 40)
        
        settings = {
            "max_system_memory_gb": 4.0,
            "max_gpu_memory_gb": 2.0,
            "batch_size": 1,
            "chunk_size": 1000,
            "use_gpu": False,
            "use_mixed_precision": False
        }
        
        # System memory settings
        if "system_memory" in resources:
            memory_gb = resources["system_memory"]["total_gb"]
            available_gb = resources["system_memory"]["available_gb"]
            
            if memory_gb >= 16.0:
                settings["max_system_memory_gb"] = 8.0
                settings["batch_size"] = 2
                settings["chunk_size"] = 2000
                print("📊 High-performance settings (16GB+ RAM)")
            elif memory_gb >= 8.0:
                settings["max_system_memory_gb"] = 6.0
                settings["batch_size"] = 1
                settings["chunk_size"] = 1500
                print("📊 Standard settings (8GB+ RAM)")
            else:
                settings["max_system_memory_gb"] = min(4.0, available_gb * 0.8)
                settings["batch_size"] = 1
                settings["chunk_size"] = 500
                print("📊 Memory-efficient settings (<8GB RAM)")
        
        # GPU settings
        if "gpu" in resources:
            gpu_memory_gb = resources["gpu"]["memory_gb"]
            
            if gpu_memory_gb >= 6.0:
                settings["max_gpu_memory_gb"] = 4.0
                settings["use_gpu"] = True
                settings["use_mixed_precision"] = True
                print("🎮 GPU acceleration enabled (6GB+ VRAM)")
            elif gpu_memory_gb >= 4.0:
                settings["max_gpu_memory_gb"] = 2.5
                settings["use_gpu"] = True
                settings["use_mixed_precision"] = False
                print("🎮 GPU acceleration enabled (4GB+ VRAM)")
            else:
                settings["use_gpu"] = False
                print("🎮 CPU-only mode (limited VRAM)")
        else:
            print("🎮 CPU-only mode (no GPU)")
        
        # Display settings
        print(f"\n📋 Recommended Configuration:")
        print(f"   Max System Memory: {settings['max_system_memory_gb']:.1f}GB")
        print(f"   Max GPU Memory: {settings['max_gpu_memory_gb']:.1f}GB")
        print(f"   Batch Size: {settings['batch_size']}")
        print(f"   Chunk Size: {settings['chunk_size']}")
        print(f"   Use GPU: {'Yes' if settings['use_gpu'] else 'No'}")
        print(f"   Mixed Precision: {'Yes' if settings['use_mixed_precision'] else 'No'}")
        
        return settings
    
    def run_monitoring(self):
        """Run real-time resource monitoring"""
        print("📊 Real-Time Resource Monitoring")
        print("=" * 40)
        print("Press Ctrl+C to stop monitoring\n")
        
        self.running = True
        
        try:
            while self.running:
                # Clear screen (works on most terminals)
                print("\033[2J\033[H", end="")
                
                # Current timestamp
                current_time = time.strftime("%Y-%m-%d %H:%M:%S")
                print(f"🕒 {current_time}")
                print("=" * 50)
                
                # Get current status
                status = self.resource_manager.monitor.get_current_status()
                
                # System memory
                print(f"💾 System Memory:")
                print(f"   Used: {status.system_memory_used_gb:.1f}GB ({status.system_memory_percent:.1f}%)")
                print(f"   Available: {status.system_memory_available_gb:.1f}GB")
                
                # Memory status indicator
                if status.system_memory_percent > 85:
                    print(f"   Status: 🔴 CRITICAL")
                elif status.system_memory_percent > 75:
                    print(f"   Status: 🟡 WARNING")
                else:
                    print(f"   Status: 🟢 GOOD")
                
                # GPU memory
                if torch.cuda.is_available():
                    print(f"\n🎮 GPU Memory:")
                    print(f"   Used: {status.gpu_memory_used_gb:.1f}GB ({status.gpu_memory_percent:.1f}%)")
                    print(f"   Available: {status.gpu_memory_available_gb:.1f}GB")
                    
                    # GPU status indicator
                    if status.gpu_memory_percent > 85:
                        print(f"   Status: 🔴 CRITICAL")
                    elif status.gpu_memory_percent > 75:
                        print(f"   Status: 🟡 WARNING")
                    else:
                        print(f"   Status: 🟢 GOOD")
                
                # CPU usage
                print(f"\n🖥️  CPU Usage: {status.cpu_percent:.1f}%")
                
                # Resource manager statistics
                summary = self.resource_manager.get_resource_summary()
                alerts = summary.get("alerts", {})
                
                if alerts.get("memory_warnings", 0) > 0 or alerts.get("critical_events", 0) > 0:
                    print(f"\n⚠️  Alerts:")
                    if alerts.get("memory_warnings", 0) > 0:
                        print(f"   Memory warnings: {alerts['memory_warnings']}")
                    if alerts.get("critical_events", 0) > 0:
                        print(f"   Critical events: {alerts['critical_events']}")
                
                # Recommendations
                if status.system_memory_percent > 80:
                    print(f"\n💡 Recommendations:")
                    print(f"   • Close unnecessary applications")
                    print(f"   • Use shorter audio files")
                    print(f"   • Restart system if memory usage is high")
                
                print(f"\n" + "=" * 50)
                print(f"Update interval: {self.update_interval}s | Press Ctrl+C to stop")
                
                # Wait for next update
                time.sleep(self.update_interval)
                
        except KeyboardInterrupt:
            pass
        finally:
            self.running = False
            print(f"\n🛑 Monitoring stopped")
    
    def cleanup(self):
        """Cleanup resources"""
        if hasattr(self, 'resource_manager'):
            self.resource_manager.cleanup_memory()


def main():
    """Main function"""
    parser = argparse.ArgumentParser(
        description="Resource Monitoring for Phase 6 TJA Generator",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Detect system resources
  python monitor_resources.py detect
  
  # Get optimal settings
  python monitor_resources.py optimize
  
  # Run real-time monitoring
  python monitor_resources.py monitor --interval 1.0
        """
    )
    
    subparsers = parser.add_subparsers(dest="command", help="Available commands")
    
    # Detect command
    detect_parser = subparsers.add_parser("detect", help="Detect system resources")
    
    # Optimize command
    optimize_parser = subparsers.add_parser("optimize", help="Get optimal settings")
    
    # Monitor command
    monitor_parser = subparsers.add_parser("monitor", help="Real-time monitoring")
    monitor_parser.add_argument("--interval", type=float, default=2.0, help="Update interval in seconds")
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    # Create monitor app
    app = ResourceMonitorApp()
    
    try:
        if args.command == "detect":
            resources = app.detect_system_resources()
            
        elif args.command == "optimize":
            resources = app.detect_system_resources()
            settings = app.get_optimal_settings(resources)
            
            # Save settings to file
            import json
            settings_file = Path("optimal_phase6_settings.json")
            with open(settings_file, 'w') as f:
                json.dump(settings, f, indent=2)
            
            print(f"\n💾 Settings saved to: {settings_file}")
            
        elif args.command == "monitor":
            app.update_interval = args.interval
            app.run_monitoring()
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        app.cleanup()


if __name__ == "__main__":
    main()
