#!/usr/bin/env python3
"""
Phase 6 Test Suite

Comprehensive test suite for Phase 6 inference pipeline and validation system
including unit tests, integration tests, and system validation tests.
"""

import unittest
import sys
import time
import tempfile
import shutil
import numpy as np
import torch
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Phase 6 imports
from src.phase6 import (
    create_phase6_config, validate_environment, Phase6Config,
    TJAInferenceSystem, PerformanceBenchmark, TJAValidator
)
from src.phase6.audio_preprocessing import AudioPreprocessor, SpectralFeatureExtractor
from src.phase6.tja_postprocessing import TJAPostProcessor, TJAFormatValidator, TJAChart, TJANote
from src.phase6.validation_framework import MusicalCoherenceValidator, DifficultyValidator, TimingValidator
from src.phase6.performance_benchmark import InferencePerformanceMonitor


class TestPhase6Configuration(unittest.TestCase):
    """Test Phase 6 configuration system"""
    
    def test_create_phase6_config(self):
        """Test configuration creation"""
        config = create_phase6_config(
            experiment_name="test_config",
            debug_mode=True
        )
        
        self.assertIsInstance(config, Phase6Config)
        self.assertEqual(config.experiment_name, "test_config")
        self.assertTrue(config.debug_mode)
        self.assertEqual(config.log_level, "DEBUG")
    
    def test_config_validation(self):
        """Test configuration validation"""
        config = create_phase6_config()
        
        # Test valid configuration
        self.assertIsNotNone(config.inference)
        self.assertIsNotNone(config.validation)
        self.assertIsNotNone(config.benchmark)
        self.assertIsNotNone(config.deployment)
    
    def test_config_serialization(self):
        """Test configuration save/load"""
        config = create_phase6_config(experiment_name="serialization_test")
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            config.save_config(f.name)
            
            # Load configuration
            loaded_config = Phase6Config.load_config(f.name)
            
            self.assertEqual(loaded_config.experiment_name, "serialization_test")
            self.assertEqual(loaded_config.inference.model_path, config.inference.model_path)
        
        # Cleanup
        Path(f.name).unlink(missing_ok=True)
    
    def test_environment_validation(self):
        """Test environment validation"""
        results = validate_environment()
        
        self.assertIsInstance(results, dict)
        self.assertIn("valid", results)
        self.assertIn("dependencies", results)
        self.assertIn("pytorch", results["dependencies"])


class TestAudioPreprocessing(unittest.TestCase):
    """Test audio preprocessing pipeline"""
    
    def setUp(self):
        """Setup test configuration"""
        self.config = {
            "sample_rate": 44100,
            "frame_rate": 50,
            "spectral_features": 80,
            "rhythmic_features": 60,
            "temporal_features": 61,
            "normalize_audio": True,
            "apply_preemphasis": True
        }
    
    def test_spectral_feature_extractor(self):
        """Test spectral feature extraction"""
        try:
            extractor = SpectralFeatureExtractor(self.config)
            
            # Create synthetic audio
            audio = np.random.randn(44100)  # 1 second of audio
            sr = 44100
            
            features = extractor.extract_features(audio, sr)
            
            self.assertEqual(features.shape[1], 80)  # 80 spectral features
            self.assertGreater(features.shape[0], 0)  # Should have time frames
            
        except ImportError:
            self.skipTest("librosa not available")
    
    def test_audio_preprocessor_initialization(self):
        """Test audio preprocessor initialization"""
        try:
            preprocessor = AudioPreprocessor(self.config)
            
            self.assertEqual(preprocessor.sample_rate, 44100)
            self.assertEqual(preprocessor.frame_rate, 50)
            self.assertTrue(preprocessor.normalize_audio)
            
        except ImportError:
            self.skipTest("librosa not available")
    
    @patch('src.phase6.audio_preprocessing.librosa')
    def test_audio_preprocessing_mock(self, mock_librosa):
        """Test audio preprocessing with mocked librosa"""
        # Mock librosa functions
        mock_librosa.load.return_value = (np.random.randn(44100), 44100)
        mock_librosa.feature.melspectrogram.return_value = np.random.randn(80, 100)
        mock_librosa.power_to_db.return_value = np.random.randn(80, 100)
        mock_librosa.onset.onset_detect.return_value = np.array([10, 20, 30])
        mock_librosa.onset.onset_strength.return_value = np.random.randn(100)
        mock_librosa.beat.beat_track.return_value = (120, np.array([0, 20, 40]))
        mock_librosa.feature.spectral_centroid.return_value = np.random.randn(1, 100)
        mock_librosa.feature.spectral_rolloff.return_value = np.random.randn(1, 100)
        mock_librosa.feature.zero_crossing_rate.return_value = np.random.randn(1, 100)
        mock_librosa.feature.spectral_bandwidth.return_value = np.random.randn(1, 100)
        
        preprocessor = AudioPreprocessor(self.config)
        
        # Test with mock data
        with tempfile.NamedTemporaryFile(suffix='.wav') as temp_audio:
            features, timing_grid = preprocessor.process_audio(
                temp_audio.name, bpm=120.0, offset=0.0
            )
            
            self.assertEqual(features.shape[1], 201)  # 80 + 60 + 61 = 201
            self.assertIn("times", timing_grid)
            self.assertIn("bpm", timing_grid)


class TestTJAPostProcessing(unittest.TestCase):
    """Test TJA post-processing pipeline"""
    
    def setUp(self):
        """Setup test configuration"""
        self.config = {
            "note_threshold": 0.5,
            "density_smoothing": True,
            "pattern_coherence_check": True,
            "quantization_enabled": True,
            "minimum_note_gap": 0.05,
            "max_notes_per_measure": 32
        }
    
    def test_tja_format_validator(self):
        """Test TJA format validation"""
        validator = TJAFormatValidator()
        
        # Create test chart
        test_notes = [
            TJANote(time=0.0, note_type=1, position=0.0, measure=0),
            TJANote(time=0.5, note_type=2, position=2.0, measure=0),
            TJANote(time=1.0, note_type=1, position=4.0, measure=1)
        ]
        
        test_chart = TJAChart(
            title="Test Chart",
            artist="Test Artist",
            bpm=120.0,
            offset=0.0,
            difficulty_level=9,
            course_type="oni",
            notes=test_notes,
            metadata={},
            measures=["12,", "1,"]
        )
        
        results = validator.validate_chart(test_chart)
        
        self.assertIsInstance(results, dict)
        self.assertIn("valid", results)
        self.assertIn("format_score", results)
        self.assertGreaterEqual(results["format_score"], 0.0)
        self.assertLessEqual(results["format_score"], 1.0)
    
    def test_tja_postprocessor(self):
        """Test TJA post-processor"""
        postprocessor = TJAPostProcessor(self.config)
        
        # Create mock model outputs
        model_outputs = {
            "logits": torch.randn(100, 8)  # 100 frames, 8 note types
        }
        
        timing_grid = {
            "times": np.linspace(0, 10, 100),
            "beat_positions": np.linspace(0, 40, 100),
            "measure_positions": np.linspace(0, 10, 100),
            "bpm": 120.0,
            "offset": 0.0,
            "frame_rate": 10,
            "n_frames": 100
        }
        
        chart = postprocessor.generate_tja(
            model_outputs=model_outputs,
            timing_grid=timing_grid,
            bpm=120.0,
            offset=0.0,
            difficulty_level=9,
            course_type="oni"
        )
        
        self.assertIsInstance(chart, TJAChart)
        self.assertEqual(chart.bpm, 120.0)
        self.assertEqual(chart.difficulty_level, 9)
        self.assertIsInstance(chart.notes, list)
        self.assertIsInstance(chart.measures, list)
    
    def test_chart_to_tja_string(self):
        """Test TJA string generation"""
        postprocessor = TJAPostProcessor(self.config)
        
        # Create test chart
        test_notes = [
            TJANote(time=0.0, note_type=1, position=0.0, measure=0),
            TJANote(time=0.5, note_type=2, position=2.0, measure=0)
        ]
        
        test_chart = TJAChart(
            title="Test Chart",
            artist="Test Artist",
            bpm=120.0,
            offset=0.0,
            difficulty_level=9,
            course_type="oni",
            notes=test_notes,
            metadata={},
            measures=["1020000000000000,"]
        )
        
        tja_string = postprocessor.chart_to_tja_string(test_chart)
        
        self.assertIsInstance(tja_string, str)
        self.assertIn("TITLE:Test Chart", tja_string)
        self.assertIn("ARTIST:Test Artist", tja_string)
        self.assertIn("BPM:120.0", tja_string)
        self.assertIn("#START", tja_string)
        self.assertIn("#END", tja_string)


class TestValidationFramework(unittest.TestCase):
    """Test validation framework"""
    
    def setUp(self):
        """Setup test configuration"""
        self.config = {
            "enable_format_validation": True,
            "enable_musical_validation": True,
            "enable_difficulty_validation": True,
            "enable_timing_validation": True,
            "minimum_quality_score": 0.7,
            "validation_weights": {
                "format_validation": 0.2,
                "musical_validation": 0.3,
                "difficulty_validation": 0.3,
                "timing_validation": 0.2
            }
        }
    
    def test_musical_coherence_validator(self):
        """Test musical coherence validation"""
        validator = MusicalCoherenceValidator(self.config)
        
        # Create test chart
        test_notes = []
        for i in range(16):
            note = TJANote(
                time=i * 0.5,
                note_type=1 if i % 2 == 0 else 2,  # Alternating pattern
                position=i * 2,
                measure=i // 4
            )
            test_notes.append(note)
        
        test_chart = TJAChart(
            title="Test Chart",
            artist="Test Artist",
            bpm=120.0,
            offset=0.0,
            difficulty_level=9,
            course_type="oni",
            notes=test_notes,
            metadata={},
            measures=["1212,"] * 4
        )
        
        result = validator.validate(test_chart, {})
        
        self.assertEqual(result.category, "musical_coherence")
        self.assertIsInstance(result.score, float)
        self.assertGreaterEqual(result.score, 0.0)
        self.assertLessEqual(result.score, 1.0)
        self.assertIsInstance(result.passed, bool)
    
    def test_difficulty_validator(self):
        """Test difficulty validation"""
        validator = DifficultyValidator(self.config)
        
        # Create test chart with appropriate difficulty
        test_notes = []
        for i in range(20):  # Moderate density
            note = TJANote(
                time=i * 0.3,  # ~3.3 notes per second
                note_type=np.random.choice([1, 2, 3]),
                position=i * 1.2,
                measure=i // 4
            )
            test_notes.append(note)
        
        test_chart = TJAChart(
            title="Test Chart",
            artist="Test Artist",
            bpm=120.0,
            offset=0.0,
            difficulty_level=9,
            course_type="oni",
            notes=test_notes,
            metadata={},
            measures=["1212,"] * 5
        )
        
        result = validator.validate(test_chart, {})
        
        self.assertEqual(result.category, "difficulty_validation")
        self.assertIsInstance(result.score, float)
        self.assertGreaterEqual(result.score, 0.0)
        self.assertLessEqual(result.score, 1.0)
    
    def test_timing_validator(self):
        """Test timing validation"""
        validator = TimingValidator(self.config)
        
        # Create test chart with good timing
        test_notes = []
        for i in range(8):
            note = TJANote(
                time=i * 0.5,  # Regular intervals
                note_type=1,
                position=i * 2,
                measure=i // 4
            )
            test_notes.append(note)
        
        test_chart = TJAChart(
            title="Test Chart",
            artist="Test Artist",
            bpm=120.0,
            offset=0.0,
            difficulty_level=9,
            course_type="oni",
            notes=test_notes,
            metadata={},
            measures=["1010,", "1010,"]
        )
        
        result = validator.validate(test_chart, {})
        
        self.assertEqual(result.category, "timing_validation")
        self.assertIsInstance(result.score, float)
        self.assertGreaterEqual(result.score, 0.0)
        self.assertLessEqual(result.score, 1.0)
    
    def test_tja_validator_integration(self):
        """Test complete TJA validator"""
        validator = TJAValidator(self.config)
        
        # Create comprehensive test chart
        test_notes = []
        for i in range(12):
            note = TJANote(
                time=i * 0.5,
                note_type=1 if i % 2 == 0 else 2,
                position=i * 2,
                measure=i // 4
            )
            test_notes.append(note)
        
        test_chart = TJAChart(
            title="Integration Test Chart",
            artist="Test Artist",
            bpm=120.0,
            offset=0.0,
            difficulty_level=9,
            course_type="oni",
            notes=test_notes,
            metadata={},
            measures=["1212,"] * 3
        )
        
        results = validator.validate_generated_chart(
            test_chart, 
            {"bpm": 120.0, "difficulty_level": 9}
        )
        
        self.assertIsInstance(results, dict)
        self.assertIn("overall_score", results)
        self.assertIn("overall_passed", results)
        self.assertIn("component_results", results)
        self.assertIn("summary", results)


class TestPerformanceBenchmark(unittest.TestCase):
    """Test performance benchmarking system"""
    
    def test_inference_performance_monitor(self):
        """Test inference performance monitoring"""
        monitor = InferencePerformanceMonitor()
        
        # Record test metrics
        test_metrics = {
            "total_time": 1.5,
            "inference_time": 1.0,
            "preprocessing_time": 0.3,
            "postprocessing_time": 0.2,
            "memory_usage": {"gpu_after_gb": 2.5},
            "realtime_factor": 10.0,
            "quality_score": 0.85
        }
        
        monitor.record_inference(test_metrics)
        
        stats = monitor.get_statistics()
        
        self.assertEqual(stats["total_inferences"], 1)
        self.assertEqual(stats["average_inference_time"], 1.5)
        self.assertEqual(stats["average_quality_score"], 0.85)
    
    def test_performance_benchmark_initialization(self):
        """Test performance benchmark initialization"""
        # Create mock inference system
        mock_inference_system = Mock()
        
        # Create benchmark config
        from src.phase6.config import BenchmarkConfig
        benchmark_config = BenchmarkConfig()
        
        benchmark = PerformanceBenchmark(benchmark_config, mock_inference_system)
        
        self.assertIsNotNone(benchmark.config)
        self.assertIsNotNone(benchmark.inference_system)
        self.assertIsInstance(benchmark.test_cases, list)


class TestSystemIntegration(unittest.TestCase):
    """Test system integration"""
    
    def test_phase6_config_integration(self):
        """Test Phase 6 configuration integration"""
        config = create_phase6_config(
            experiment_name="integration_test",
            enable_validation=True,
            enable_benchmarking=True
        )
        
        # Test configuration access methods
        inference_config = config.get_inference_config()
        validation_config = config.get_validation_config()
        benchmark_config = config.get_benchmark_config()
        
        self.assertIsInstance(inference_config, dict)
        self.assertIsInstance(validation_config, dict)
        self.assertIsInstance(benchmark_config, dict)
        
        self.assertIn("model_path", inference_config)
        self.assertIn("enable_format_validation", validation_config)
        self.assertIn("enable_speed_benchmark", benchmark_config)
    
    @patch('src.phase6.inference_system.TJAGeneratorModel')
    @patch('torch.load')
    def test_inference_system_initialization(self, mock_torch_load, mock_model_class):
        """Test inference system initialization with mocks"""
        # Mock model loading
        mock_torch_load.return_value = {
            "model_state_dict": {},
            "model_config": {
                "vocab_size": 8,
                "hidden_size": 512,
                "num_layers": 6,
                "num_heads": 8,
                "dropout": 0.1
            }
        }
        
        # Mock model
        mock_model = Mock()
        mock_model_class.return_value = mock_model
        
        # Create config with mock model path
        config = create_phase6_config()
        
        # Create temporary model file
        with tempfile.NamedTemporaryFile(suffix='.pt', delete=False) as temp_model:
            config.inference.model_path = temp_model.name
            
            try:
                # This should work with mocked components
                inference_system = TJAInferenceSystem(config)
                
                self.assertIsNotNone(inference_system.model)
                self.assertIsNotNone(inference_system.preprocessor)
                self.assertIsNotNone(inference_system.postprocessor)
                self.assertIsNotNone(inference_system.validator)
                
            except Exception as e:
                # Expected to fail due to missing dependencies, but should initialize components
                self.assertIn("librosa", str(e).lower())
            
            finally:
                # Cleanup
                Path(temp_model.name).unlink(missing_ok=True)


def run_tests():
    """Run all Phase 6 tests"""
    print("🧪 Phase 6 Test Suite")
    print("=" * 30)
    
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test classes
    test_classes = [
        TestPhase6Configuration,
        TestAudioPreprocessing,
        TestTJAPostProcessing,
        TestValidationFramework,
        TestPerformanceBenchmark,
        TestSystemIntegration
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Print summary
    print(f"\n📊 Test Results:")
    print(f"   Tests run: {result.testsRun}")
    print(f"   Failures: {len(result.failures)}")
    print(f"   Errors: {len(result.errors)}")
    print(f"   Skipped: {len(result.skipped)}")
    
    success_rate = (result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100
    print(f"   Success rate: {success_rate:.1f}%")
    
    if result.failures:
        print(f"\n❌ Failures:")
        for test, traceback in result.failures:
            print(f"   {test}: {traceback.split('AssertionError:')[-1].strip()}")
    
    if result.errors:
        print(f"\n⚠️  Errors:")
        for test, traceback in result.errors:
            print(f"   {test}: {traceback.split('Exception:')[-1].strip()}")
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
